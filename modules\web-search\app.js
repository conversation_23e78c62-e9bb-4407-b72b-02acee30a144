// Türkçe: Web arama modülü - internet araması yapar

;((context) => {
  const webSearchModule = {
    searchResults: [],
    selectedResult: null,
    currentQuery: "",
    searchHistory: JSON.parse(localStorage.getItem("web_search_history") || "[]"),

    init() {
      this.renderSearchInterface()
      this.setupEventListeners()
      context.updateStatus("Web arama modülü hazır")
    },

    renderSearchInterface() {
      // Türkçe: Sol panel - arama sonuçları
      context.sidebar.innerHTML = `
        <div class="web-search-panel">
          <div class="search-header">
            <h3><i class="fas fa-globe"></i> Web Arama</h3>
            <div class="search-input-container">
              <input type="text" id="web-search-input" placeholder="Aranacak terimi girin..." class="search-input-field">
              <button id="web-search-btn" class="search-btn">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
          
          <div class="search-history" id="search-history">
            <h4><i class="fas fa-history"></i> Son Aramalar</h4>
            <div class="history-list" id="history-list">
              ${this.renderSearchHistory()}
            </div>
          </div>
          
          <div class="search-results" id="search-results">
            <div class="no-results">
              <i class="fas fa-search"></i>
              <p>Arama yapmak için yukarıdaki alana terim yazın</p>
            </div>
          </div>
        </div>
      `

      // Türkçe: Sağ panel - seçili sonuç önizleme
      context.content.innerHTML = `
        <div class="result-preview">
          <div class="preview-placeholder">
            <i class="fas fa-globe-americas"></i>
            <h3>Web Sonucu Önizleme</h3>
            <p>Bir arama sonucu seçin ve burada önizlemesini görün</p>
            <div class="search-engines">
              <h4>Desteklenen Arama Motorları:</h4>
              <div class="engine-list">
                <div class="engine-item">
                  <i class="fab fa-google"></i>
                  <span>Google</span>
                </div>
                <div class="engine-item">
                  <i class="fab fa-microsoft"></i>
                  <span>Bing</span>
                </div>
                <div class="engine-item">
                  <i class="fas fa-search"></i>
                  <span>DuckDuckGo</span>
                </div>
                <div class="engine-item">
                  <i class="fab fa-yandex"></i>
                  <span>Yandex</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      `
    },

    setupEventListeners() {
      const searchInput = document.getElementById("web-search-input")
      const searchBtn = document.getElementById("web-search-btn")

      // Türkçe: Arama input olayları
      searchInput.addEventListener("input", (e) => {
        if (e.target.value.length > 2) {
          this.showSearchSuggestions(e.target.value)
        }
      })

      searchInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter") {
          this.performSearch(e.target.value)
        }
      })

      searchBtn.addEventListener("click", () => {
        this.performSearch(searchInput.value)
      })

      // Türkçe: Arama geçmişi tıklama olayları
      this.attachHistoryEventListeners()
    },

    attachHistoryEventListeners() {
      const historyItems = document.querySelectorAll(".history-item")
      historyItems.forEach((item) => {
        item.addEventListener("click", () => {
          const query = item.dataset.query
          document.getElementById("web-search-input").value = query
          this.performSearch(query)
        })

        const deleteBtn = item.querySelector(".delete-history-btn")
        if (deleteBtn) {
          deleteBtn.addEventListener("click", (e) => {
            e.stopPropagation()
            this.removeFromHistory(item.dataset.query)
          })
        }
      })
    },

    renderSearchHistory() {
      if (this.searchHistory.length === 0) {
        return '<div class="no-history">Henüz arama geçmişi yok</div>'
      }

      return this.searchHistory
        .slice(0, 5)
        .map(
          (item) => `
        <div class="history-item" data-query="${item.query}">
          <div class="history-content">
            <span class="history-query">${item.query}</span>
            <span class="history-time">${this.getTimeAgo(item.timestamp)}</span>
          </div>
          <button class="delete-history-btn" title="Sil">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `,
        )
        .join("")
    },

    getTimeAgo(timestamp) {
      const now = Date.now()
      const diff = now - timestamp

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 1) return "Az önce"
      if (minutes < 60) return `${minutes}dk önce`
      if (hours < 24) return `${hours}s önce`
      return `${days}g önce`
    },

    async performSearch(query) {
      if (!query || query.length < 2) return

      this.currentQuery = query
      this.addToHistory(query)
      context.updateStatus(`"${query}" aranıyor...`)

      try {
        // Türkçe: Seçili arama motorunu al
        const searchEngine = this.getSelectedSearchEngine()

        // Türkçe: Arama sonuçlarını simüle et (gerçek API entegrasyonu için)
        const results = await this.simulateWebSearch(query, searchEngine)

        this.searchResults = results
        this.renderSearchResults()

        context.updateStatus(`${results.length} sonuç bulundu`)
      } catch (error) {
        console.error("Web arama hatası:", error)
        context.updateStatus("Arama sırasında hata oluştu")
      }
    },

    getSelectedSearchEngine() {
      const engineFilter = context.app.activeFilters.find((f) => f.id === "search_engine")
      return engineFilter ? engineFilter.value : "Google"
    },

    async simulateWebSearch(query, engine) {
      // Türkçe: Gerçek API entegrasyonu için bu fonksiyon değiştirilmeli
      // Şimdilik simüle edilmiş sonuçlar döndürüyoruz

      await new Promise((resolve) => setTimeout(resolve, 1000)) // Simüle edilmiş gecikme

      const mockResults = [
        {
          title: `${query} - Wikipedia`,
          url: `https://tr.wikipedia.org/wiki/${encodeURIComponent(query)}`,
          description: `${query} hakkında detaylı bilgi. Wikipedia'da ${query} ile ilgili kapsamlı makale.`,
          domain: "wikipedia.org",
          favicon: "https://wikipedia.org/favicon.ico",
        },
        {
          title: `${query} Nedir? - Detaylı Açıklama`,
          url: `https://example.com/search?q=${encodeURIComponent(query)}`,
          description: `${query} konusunda detaylı açıklama ve örnekler. ${query} hakkında bilmeniz gereken her şey.`,
          domain: "example.com",
          favicon: "https://example.com/favicon.ico",
        },
        {
          title: `${query} - YouTube`,
          url: `https://youtube.com/results?search_query=${encodeURIComponent(query)}`,
          description: `${query} ile ilgili videolar. YouTube'da ${query} hakkında en popüler videolar.`,
          domain: "youtube.com",
          favicon: "https://youtube.com/favicon.ico",
        },
        {
          title: `${query} Haberleri - Son Dakika`,
          url: `https://news.example.com/search?q=${encodeURIComponent(query)}`,
          description: `${query} ile ilgili son haberler ve gelişmeler. Güncel ${query} haberleri.`,
          domain: "news.example.com",
          favicon: "https://news.example.com/favicon.ico",
        },
        {
          title: `${query} - GitHub`,
          url: `https://github.com/search?q=${encodeURIComponent(query)}`,
          description: `${query} ile ilgili açık kaynak projeler. GitHub'da ${query} repositories.`,
          domain: "github.com",
          favicon: "https://github.com/favicon.ico",
        },
      ]

      return mockResults.map((result, index) => ({
        ...result,
        id: Date.now() + index,
        searchEngine: engine,
        timestamp: Date.now(),
      }))
    },

    renderSearchResults() {
      const resultsContainer = document.getElementById("search-results")

      if (this.searchResults.length === 0) {
        resultsContainer.innerHTML = `
          <div class="no-results">
            <i class="fas fa-search"></i>
            <p>"${this.currentQuery}" için sonuç bulunamadı</p>
          </div>
        `
        return
      }

      const resultsHTML = `
        <div class="results-header">
          <span class="results-count">${this.searchResults.length} sonuç</span>
          <span class="search-engine">via ${this.getSelectedSearchEngine()}</span>
        </div>
        <div class="results-list">
          ${this.searchResults
            .map(
              (result, index) => `
            <div class="result-item" data-index="${index}" data-url="${result.url}">
              <div class="result-favicon">
                <img src="${result.favicon}" alt="" onerror="this.style.display='none'">
                <i class="fas fa-globe" style="display: none;"></i>
              </div>
              <div class="result-content">
                <div class="result-title">${result.title}</div>
                <div class="result-url">${result.url}</div>
                <div class="result-description">${result.description}</div>
                <div class="result-meta">
                  <span class="result-domain">${result.domain}</span>
                  <span class="result-engine">${result.searchEngine}</span>
                </div>
              </div>
              <div class="result-actions">
                <button class="result-action-btn" title="Aç">
                  <i class="fas fa-external-link-alt"></i>
                </button>
                <button class="result-action-btn" title="Kopyala">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>
          `,
            )
            .join("")}
        </div>
      `

      resultsContainer.innerHTML = resultsHTML

      // Türkçe: Sonuç öğelerine olay dinleyicileri ekle
      this.attachResultEventListeners()
    },

    attachResultEventListeners() {
      const resultItems = document.querySelectorAll(".result-item")

      resultItems.forEach((item, index) => {
        // Türkçe: Sonuç seçimi
        item.addEventListener("click", (e) => {
          if (!e.target.closest(".result-actions")) {
            this.selectResult(index)
          }
        })

        // Türkçe: Çift tıklama ile aç
        item.addEventListener("dblclick", () => {
          this.openResult(index)
        })

        // Türkçe: Eylem butonları
        const actionBtns = item.querySelectorAll(".result-action-btn")
        actionBtns[0].addEventListener("click", (e) => {
          e.stopPropagation()
          this.openResult(index)
        })

        actionBtns[1].addEventListener("click", (e) => {
          e.stopPropagation()
          this.copyResultUrl(index)
        })
      })

      // Türkçe: Favicon yükleme hatası durumunda ikon göster
      const favicons = document.querySelectorAll(".result-favicon img")
      favicons.forEach((img) => {
        img.addEventListener("error", () => {
          img.style.display = "none"
          img.nextElementSibling.style.display = "inline"
        })
      })
    },

    selectResult(index) {
      // Türkçe: Önceki seçimi temizle
      const prevSelected = document.querySelector(".result-item.selected")
      if (prevSelected) {
        prevSelected.classList.remove("selected")
      }

      // Türkçe: Yeni seçimi işaretle
      const resultItems = document.querySelectorAll(".result-item")
      if (resultItems[index]) {
        resultItems[index].classList.add("selected")
        this.selectedResult = this.searchResults[index]
        this.showResultPreview(this.selectedResult)
        context.updateSelection(`${this.selectedResult.title} seçildi`)
      }
    },

    showResultPreview(result) {
      const previewContainer = context.content.querySelector(".result-preview")

      previewContainer.innerHTML = `
        <div class="preview-header">
          <div class="preview-favicon">
            <img src="${result.favicon}" alt="" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
            <i class="fas fa-globe" style="display: none;"></i>
          </div>
          <div class="preview-info">
            <h2 class="preview-title">${result.title}</h2>
            <a href="${result.url}" class="preview-url" target="_blank">${result.url}</a>
            <div class="preview-meta">
              <span><i class="fas fa-search"></i> ${result.searchEngine}</span>
              <span><i class="fas fa-globe"></i> ${result.domain}</span>
              <span><i class="fas fa-clock"></i> ${this.getTimeAgo(result.timestamp)}</span>
            </div>
          </div>
        </div>
        
        <div class="preview-content">
          <div class="preview-description">
            <h3>Açıklama</h3>
            <p>${result.description}</p>
          </div>
          
          <div class="preview-iframe-container">
            <h3>Sayfa Önizleme</h3>
            <div class="iframe-placeholder">
              <i class="fas fa-external-link-alt"></i>
              <p>Sayfayı görüntülemek için "Aç" butonuna tıklayın</p>
              <button class="preview-open-btn" onclick="window.open('${result.url}', '_blank')">
                <i class="fas fa-external-link-alt"></i>
                Sayfayı Aç
              </button>
            </div>
          </div>
        </div>
        
        <div class="preview-actions">
          <button class="preview-action-btn primary" onclick="window.open('${result.url}', '_blank')">
            <i class="fas fa-external-link-alt"></i> Sayfayı Aç
          </button>
          <button class="preview-action-btn" onclick="window.currentModuleInstance.copyResultUrl()">
            <i class="fas fa-copy"></i> URL Kopyala
          </button>
          <button class="preview-action-btn" onclick="window.currentModuleInstance.addToBookmarks()">
            <i class="fas fa-bookmark"></i> Yer İmi Ekle
          </button>
        </div>
      `
    },

    openResult(index = null) {
      const result = index !== null ? this.searchResults[index] : this.selectedResult
      if (!result) return

      // Türkçe: Yeni sekmede aç
      window.open(result.url, "_blank")
      context.updateStatus(`${result.domain} açıldı`)
    },

    copyResultUrl(index = null) {
      const result = index !== null ? this.searchResults[index] : this.selectedResult
      if (!result) return

      context.electronAPI.copyToClipboard(result.url)
      context.updateStatus("URL kopyalandı")
    },

    addToBookmarks() {
      if (!this.selectedResult) return

      // Türkçe: Basit yer imi sistemi (localStorage)
      const bookmarks = JSON.parse(localStorage.getItem("web_bookmarks") || "[]")

      const bookmark = {
        title: this.selectedResult.title,
        url: this.selectedResult.url,
        domain: this.selectedResult.domain,
        timestamp: Date.now(),
      }

      // Türkçe: Aynı URL varsa ekleme
      if (!bookmarks.find((b) => b.url === bookmark.url)) {
        bookmarks.unshift(bookmark)
        localStorage.setItem("web_bookmarks", JSON.stringify(bookmarks))
        context.updateStatus("Yer imi eklendi")
      } else {
        context.updateStatus("Bu sayfa zaten yer imlerinde")
      }
    },

    addToHistory(query) {
      // Türkçe: Aynı sorguyu tekrar ekleme
      this.searchHistory = this.searchHistory.filter((item) => item.query !== query)

      this.searchHistory.unshift({
        query: query,
        timestamp: Date.now(),
      })

      // Türkçe: Maksimum 20 arama tut
      if (this.searchHistory.length > 20) {
        this.searchHistory = this.searchHistory.slice(0, 20)
      }

      localStorage.setItem("web_search_history", JSON.stringify(this.searchHistory))

      // Türkçe: Geçmiş listesini güncelle
      const historyList = document.getElementById("history-list")
      if (historyList) {
        historyList.innerHTML = this.renderSearchHistory()
        this.attachHistoryEventListeners()
      }
    },

    removeFromHistory(query) {
      this.searchHistory = this.searchHistory.filter((item) => item.query !== query)
      localStorage.setItem("web_search_history", JSON.stringify(this.searchHistory))

      const historyList = document.getElementById("history-list")
      if (historyList) {
        historyList.innerHTML = this.renderSearchHistory()
        this.attachHistoryEventListeners()
      }

      context.updateStatus("Geçmişten silindi")
    },

    showSearchSuggestions(query) {
      // Türkçe: Basit öneri sistemi
      const suggestions = this.searchHistory
        .filter((item) => item.query.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 3)
        .map((item) => item.query)

      // Türkçe: Popüler arama terimleri
      const popularTerms = [
        "JavaScript tutorial",
        "React hooks",
        "Node.js",
        "CSS Grid",
        "Python öğren",
        "Vue.js",
        "TypeScript",
        "Web development",
      ]

      const popularSuggestions = popularTerms
        .filter((term) => term.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 2)

      const allSuggestions = [...new Set([...suggestions, ...popularSuggestions])]

      if (allSuggestions.length > 0) {
        // Türkçe: Önerileri göster (basit implementasyon)
        console.log("Öneriler:", allSuggestions)
      }
    },

    executeAction(actionId) {
      switch (actionId) {
        case "open_link":
          this.openResult()
          break
        case "copy_link":
          this.copyResultUrl()
          break
        case "new_search":
          this.newSearch()
          break
      }
    },

    newSearch() {
      const searchInput = document.getElementById("web-search-input")
      if (searchInput) {
        searchInput.value = ""
        searchInput.focus()

        // Türkçe: Sonuçları temizle
        this.searchResults = []
        this.selectedResult = null

        const resultsContainer = document.getElementById("search-results")
        resultsContainer.innerHTML = `
          <div class="no-results">
            <i class="fas fa-search"></i>
            <p>Arama yapmak için yukarıdaki alana terim yazın</p>
          </div>
        `

        context.content.innerHTML = `
          <div class="result-preview">
            <div class="preview-placeholder">
              <i class="fas fa-globe-americas"></i>
              <h3>Web Sonucu Önizleme</h3>
              <p>Bir arama sonucu seçin ve burada önizlemesini görün</p>
            </div>
          </div>
        `

        context.updateStatus("Yeni arama için hazır")
      }
    },

    onFiltersChanged(filters) {
      // Türkçe: Filtreler değiştiğinde mevcut aramayı yenile
      if (this.currentQuery) {
        this.performSearch(this.currentQuery)
      }
    },
  }

  // Türkçe: Global erişim için modül instance'ını kaydet
  window.currentModuleInstance = webSearchModule

  // Türkçe: Modülü başlat
  webSearchModule.init()
})(window.context)
