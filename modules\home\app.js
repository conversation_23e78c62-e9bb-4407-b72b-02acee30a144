// Türkçe: <PERSON> sayfa modülü

const context = {} // Declare the context variable

module.exports = {
  run() {
    this.renderWelcomeScreen()
    context.updateStatus("Ana sayfa yüklendi")
  },

  stop() {
    // Türkçe: Temizlik işlemleri
  },

  handleQuery(query) {
    // Türkçe: <PERSON>, AI'ya yönlendir
    context.app.loadModule("ai")
    setTimeout(() => {
      const aiInstance = context.app.moduleInstances.get("ai")
      if (aiInstance && aiInstance.handleQuery) {
        aiInstance.handleQuery(query)
      }
    }, 100)
  },

  renderWelcomeScreen() {
    context.content.innerHTML = `
      <div style="padding: 40px; text-align: center; height: 100%; overflow-y: auto;">
        <i class="fas fa-home" style="font-size: 64px; color: var(--accent-color); margin-bottom: 24px;"></i>
        <h1 style="color: var(--text-primary); margin-bottom: 16px;">Modüler Masaüstü Uygulamasına Hoş Geldiniz</h1>
        <p style="color: var(--text-secondary); margin-bottom: 32px; max-width: 600px; margin-left: auto; margin-right: auto;">
          Komutları kullanın: !fs dosya arama, !web web arama, !ai AI asistan, !clip pano geçmişi, !calc hesap makinesi
        </p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; max-width: 1000px; margin: 0 auto;">
          <div class="feature-card" onclick="context.app.loadModule('file-search')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-search" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">Dosya Arama</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Bilgisayarınızdaki dosya ve klasörleri hızlıca bulun</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!fs</code>
          </div>
          
          <div class="feature-card" onclick="context.app.loadModule('ai')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-brain" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">AI Asistan</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Gemini AI ile sohbet edin veya resim oluşturun</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!ai</code>
          </div>
          
          <div class="feature-card" onclick="context.app.loadModule('web-search')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-globe" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">Web Arama</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">İnternette arama yapın ve sonuçları görüntüleyin</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!web</code>
          </div>
          
          <div class="feature-card" onclick="context.app.loadModule('clipboard')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-clipboard" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">Pano Geçmişi</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Kopyaladığınız metinleri görüntüleyin ve yönetin</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!clip</code>
          </div>
          
          <div class="feature-card" onclick="context.app.loadModule('calculator')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-calculator" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">Hesap Makinesi</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Gelişmiş hesap makinesi ve matematiksel işlemler</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!calc</code>
          </div>
          
          <div class="feature-card" onclick="context.app.loadModule('settings')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
            <i class="fas fa-cog" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
            <h3 style="color: var(--text-primary); margin-bottom: 8px;">Ayarlar</h3>
            <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Uygulama ayarları ve konfigürasyonları</p>
            <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!settings</code>
          </div>
        </div>
        
        <div style="margin-top: 40px; padding: 24px; background: var(--bg-secondary); border-radius: 12px; border: 1px solid var(--border-color); max-width: 600px; margin-left: auto; margin-right: auto;">
          <h3 style="color: var(--text-primary); margin-bottom: 16px;"><i class="fas fa-lightbulb" style="color: var(--warning-color); margin-right: 8px;"></i>İpuçları</h3>
          <div style="text-align: left;">
            <p style="color: var(--text-secondary); margin-bottom: 8px;"><strong>Komutlar:</strong> <code style="background: var(--bg-tertiary); padding: 2px 6px; border-radius: 3px;">!</code> ile başlayan komutlar modülleri açar</p>
            <p style="color: var(--text-secondary); margin-bottom: 8px;"><strong>Arama:</strong> Herhangi bir şey yazın, otomatik olarak uygun modüle yönlendirilir</p>
            <p style="color: var(--text-secondary);"><strong>Filtreler:</strong> Her modülün kendine özel filtreleri vardır</p>
          </div>
        </div>
      </div>
    `

    // Türkçe: Hover efektleri
    const style = document.createElement("style")
    style.textContent = `
      .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px var(--shadow);
        border-color: var(--accent-color);
      }
    `
    document.head.appendChild(style)
  },
}
