/* Türkçe: Modern modüler uygulama stil dosyası */

/* Türkçe: CSS değişkenleri */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --bg-hover: #4a4a4a;
  --bg-selected: rgba(70, 130, 255, 0.2);
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;
  --border-color: #404040;
  --accent-color: #4680ff;
  --accent-hover: #5a8cff;
  --success-color: #32d74b;
  --warning-color: #ff9f0a;
  --danger-color: #ff453a;
  --shadow: rgba(0, 0, 0, 0.3);
  --border-radius: 8px;
  --border-radius-small: 6px;
}

/* Türkçe: Temel sıfırlama */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Robot<PERSON>, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  user-select: none;
  -webkit-app-region: no-drag;
  height: 100vh;
}

/* Türkçe: macOS benzeri başlık çubuğu */
.titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  background: var(--bg-secondary);
  -webkit-app-region: drag;
  border-bottom: 1px solid var(--border-color);
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 12px;
}

.traffic-lights {
  display: flex;
  gap: 8px;
  -webkit-app-region: no-drag;
}

.traffic-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.traffic-light.close {
  background: #ff5f57;
}
.traffic-light.minimize {
  background: #ffbd2e;
}
.traffic-light.maximize {
  background: #28ca42;
}

.traffic-light:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

.app-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Türkçe: Ana uygulama konteyneri */
.app-container {
  height: calc(100vh - 32px);
  display: flex;
  flex-direction: column;
}

/* Türkçe: Header - Filtreler solda, arama sağda */
.app-header {
  height: 60px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 20px;
  gap: 20px;
}

/* Türkçe: Modül seçici */
.module-selector {
  position: relative;
  flex-shrink: 0;
}

.current-module-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.current-module-btn:hover {
  background: var(--bg-hover);
}

.current-module-btn i:last-child {
  font-size: 12px;
  color: var(--text-muted);
}

.module-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px var(--shadow);
  z-index: 1000;
  display: none;
  margin-top: 4px;
}

.module-dropdown.show {
  display: block;
}

.module-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  background: none;
  color: var(--text-primary);
  width: 100%;
  text-align: left;
  font-size: 14px;
}

.module-dropdown-item:hover {
  background: var(--bg-hover);
}

.module-dropdown-item:first-child {
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.module-dropdown-item:last-child {
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.module-dropdown-item i {
  width: 16px;
  text-align: center;
}

/* Türkçe: Filtreler - Solda */
.filters-container {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

.filter-select {
  padding: 6px 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  color: var(--text-primary);
  font-size: 12px;
  outline: none;
  cursor: pointer;
  min-width: 80px;
}

.filter-radio-group {
  display: flex;
  gap: 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  padding: 4px;
}

.filter-radio {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  transition: all 0.2s ease;
}

.filter-radio:hover {
  background: var(--bg-hover);
}

.filter-radio input[type="radio"] {
  margin: 0;
}

.filter-radio input[type="radio"]:checked + span {
  color: var(--accent-color);
  font-weight: 500;
}

.api-settings-btn {
  padding: 6px 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.api-settings-btn:hover {
  background: var(--accent-color);
  color: white;
}

/* Türkçe: Ana arama - Sağda */
.main-search {
  flex: 1;
  max-width: 600px;
  margin-left: auto;
}

.search-container {
  position: relative;
}

.main-input {
  width: 100%;
  height: 40px;
  padding: 0 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.main-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(70, 128, 255, 0.2);
}

.main-input::placeholder {
  color: var(--text-muted);
}

/* Türkçe: Ana içerik */
.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Türkçe: İki panelli layout */
.two-panel-layout {
  display: flex;
  height: 100%;
}

.left-panel {
  width: 350px;
  min-width: 200px;
  max-width: 60%;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.resize-handle {
  width: 4px;
  background: var(--border-color);
  cursor: col-resize;
  transition: background-color 0.2s ease;
  position: relative;
}

.resize-handle:hover {
  background: var(--accent-color);
}

.resize-handle::after {
  content: "";
  position: absolute;
  top: 0;
  left: -2px;
  right: -2px;
  bottom: 0;
}

.right-panel {
  flex: 1;
  background: var(--bg-primary);
  overflow-y: auto;
}

/* Türkçe: Tek panelli layout */
.single-panel-layout {
  height: 100%;
  background: var(--bg-primary);
  overflow-y: auto;
}

/* Türkçe: Liste öğeleri */
.list-container {
  padding: 16px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.list-item:hover {
  background: var(--bg-hover);
}

.list-item.selected {
  background: var(--bg-selected);
  border-left: 3px solid var(--accent-color);
}

.list-item-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-item-subtitle {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-item-meta {
  font-size: 11px;
  color: var(--text-muted);
  text-align: right;
  flex-shrink: 0;
}

/* Türkçe: Önizleme paneli */
.preview-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.preview-placeholder h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--text-primary);
}

/* Türkçe: AI Sohbet alanı */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.chat-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.chat-message.assistant {
  align-self: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
}

.message-avatar.user {
  background: var(--accent-color);
  color: white;
}

.message-avatar.assistant {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.message-bubble {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  position: relative;
  word-wrap: break-word;
}

.chat-message.user .message-bubble {
  background: var(--accent-color);
  color: white;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
}

.message-time {
  font-size: 11px;
  color: var(--text-muted);
  margin-top: 4px;
}

.chat-input-container {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.chat-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.chat-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: 10px 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  resize: none;
  font-family: inherit;
}

.chat-input:focus {
  border-color: var(--accent-color);
}

.chat-send-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-color);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chat-send-btn:hover {
  background: var(--accent-hover);
}

.chat-send-btn:disabled {
  background: var(--bg-tertiary);
  cursor: not-allowed;
}

/* Türkçe: Durum çubuğu */
.status-bar {
  height: 28px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 12px;
}

.status-text {
  color: var(--text-secondary);
}

.selection-info {
  color: var(--text-muted);
}

/* Türkçe: Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-overlay.show {
  display: flex;
}

.modal {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  width: 90%;
  max-width: 500px;
  box-shadow: 0 8px 32px var(--shadow);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.form-group input:focus {
  border-color: var(--accent-color);
}

.form-group small {
  display: block;
  margin-top: 4px;
  color: var(--text-muted);
  font-size: 12px;
}

.form-group small a {
  color: var(--accent-color);
  text-decoration: none;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.btn-primary:hover {
  background: var(--accent-hover);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--bg-hover);
}

/* Türkçe: Yükleniyor durumu */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--accent-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Türkçe: Scrollbar stilleri */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Türkçe: Responsive tasarım */
@media (max-width: 768px) {
  .app-header {
    flex-wrap: wrap;
    height: auto;
    min-height: 60px;
    padding: 12px 16px;
  }

  .main-search {
    order: -1;
    width: 100%;
    margin-bottom: 12px;
    margin-left: 0;
  }

  .two-panel-layout {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    max-width: none;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .resize-handle {
    width: 100%;
    height: 4px;
    cursor: row-resize;
  }

  .right-panel {
    height: 60%;
  }
}

/* Türkçe: Yardımcı sınıflar */
.hidden {
  display: none !important;
}

.invisible {
  opacity: 0;
  pointer-events: none;
}

.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
