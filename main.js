// Türkçe: Electron ana süreç dosyası - geliştirilmiş dosya arama ile
const { app, BrowserWindow, ipcMain, shell, dialog } = require("electron")
const path = require("path")
const fs = require("fs")
const { exec } = require("child_process")
const os = require("os")

// Türkçe: Ana pencere referansı
let mainWindow

// Türkçe: Uygulama hazır olduğunda pencereyi oluştur
app.whenReady().then(() => {
  createWindow()

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit()
  }
})

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 900,
    minHeight: 600,
    frame: false,
    titleBarStyle: "hidden",
    transparent: false,
    backgroundColor: "#1a1a1a",
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "preload.js"),
    },
    show: false,
  })

  mainWindow.loadFile("renderer/index.html")

  mainWindow.once("ready-to-show", () => {
    mainWindow.show()
  })

  // mainWindow.webContents.openDevTools()
}

// Türkçe: Pencere kontrolü
ipcMain.handle("window-minimize", () => {
  mainWindow.minimize()
})

ipcMain.handle("window-maximize", () => {
  if (mainWindow.isMaximized()) {
    mainWindow.restore()
  } else {
    mainWindow.maximize()
  }
})

ipcMain.handle("window-close", () => {
  mainWindow.close()
})

// Türkçe: Modül tarama
ipcMain.handle("scan-modules", async () => {
  const modulesPath = path.join(__dirname, "modules")
  const modules = []

  try {
    if (!fs.existsSync(modulesPath)) {
      console.log("Modules klasörü bulunamadı:", modulesPath)
      return modules
    }

    const moduleDirectories = fs
      .readdirSync(modulesPath, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name)

    for (const moduleDir of moduleDirectories) {
      const configPath = path.join(modulesPath, moduleDir, "config.json")

      if (fs.existsSync(configPath)) {
        try {
          const configContent = fs.readFileSync(configPath, "utf8")
          const config = JSON.parse(configContent)

          const appPath = path.join(modulesPath, moduleDir, "app.js")
          const stylePath = path.join(modulesPath, moduleDir, "style.css")

          config.path = moduleDir
          config.hasApp = fs.existsSync(appPath)
          config.hasStyle = fs.existsSync(stylePath)

          modules.push(config)
        } catch (error) {
          console.error(`${moduleDir} modülü config.json hatası:`, error)
        }
      }
    }
  } catch (error) {
    console.error("Modüller taranırken hata:", error)
  }

  return modules
})

// Türkçe: Modül dosyalarını yükle
ipcMain.handle("load-module-files", async (event, modulePath) => {
  const moduleDir = path.join(__dirname, "modules", modulePath)
  const files = {}

  try {
    const appPath = path.join(moduleDir, "app.js")
    if (fs.existsSync(appPath)) {
      files.app = fs.readFileSync(appPath, "utf8")
    }

    const stylePath = path.join(moduleDir, "style.css")
    if (fs.existsSync(stylePath)) {
      files.style = fs.readFileSync(stylePath, "utf8")
    }

    const configPath = path.join(moduleDir, "config.json")
    if (fs.existsSync(configPath)) {
      files.config = fs.readFileSync(configPath, "utf8")
    }
  } catch (error) {
    console.error("Modül dosyaları yüklenirken hata:", error)
  }

  return files
})

// Türkçe: Gelişmiş dosya arama
ipcMain.handle("search-files", async (event, query, filters = {}) => {
  return new Promise((resolve) => {
    const homeDir = os.homedir()
    const searchPaths = getSearchPaths(filters.location, homeDir)
    const results = []

    let completedSearches = 0
    const totalSearches = searchPaths.length

    if (totalSearches === 0) {
      resolve([])
      return
    }

    searchPaths.forEach((searchPath) => {
      searchInPath(searchPath, query, filters, (pathResults) => {
        results.push(...pathResults)
        completedSearches++

        if (completedSearches === totalSearches) {
          // Türkçe: Sonuçları sırala ve sınırla
          const sortedResults = results.sort((a, b) => b.score - a.score).slice(0, 50)

          resolve(sortedResults)
        }
      })
    })
  })
})

function getSearchPaths(location, homeDir) {
  const paths = {
    Desktop: [path.join(homeDir, "Desktop")],
    Documents: [path.join(homeDir, "Documents")],
    Downloads: [path.join(homeDir, "Downloads")],
    Pictures: [path.join(homeDir, "Pictures")],
    Videos: [path.join(homeDir, "Videos")],
    Music: [path.join(homeDir, "Music")],
    All: [
      path.join(homeDir, "Desktop"),
      path.join(homeDir, "Documents"),
      path.join(homeDir, "Downloads"),
      path.join(homeDir, "Pictures"),
      path.join(homeDir, "Videos"),
      path.join(homeDir, "Music"),
    ],
  }

  return paths[location] || paths["All"]
}

function searchInPath(searchPath, query, filters, callback) {
  if (!fs.existsSync(searchPath)) {
    callback([])
    return
  }

  const results = []
  const queryLower = query.toLowerCase()

  try {
    searchDirectory(searchPath, queryLower, filters, results, 0, 3) // Max 3 seviye derinlik
  } catch (error) {
    console.error(`Arama hatası (${searchPath}):`, error)
  }

  callback(results)
}

function searchDirectory(dirPath, query, filters, results, currentDepth, maxDepth) {
  if (currentDepth > maxDepth || results.length >= 50) return

  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true })

    for (const item of items) {
      if (results.length >= 50) break

      const itemPath = path.join(dirPath, item.name)
      const itemName = item.name.toLowerCase()

      // Türkçe: Gizli dosyaları atla
      if (item.name.startsWith(".")) continue

      // Türkçe: Sistem klasörlerini atla
      if (isSystemPath(itemPath)) continue

      // Türkçe: İsim eşleşmesi kontrolü
      if (itemName.includes(query)) {
        const stats = fs.statSync(itemPath)
        const isDirectory = item.isDirectory()

        // Türkçe: Filtre kontrolü
        if (!matchesFilters(itemPath, isDirectory, filters)) continue

        const result = {
          name: item.name,
          path: itemPath,
          type: isDirectory ? "folder" : path.extname(item.name),
          size: isDirectory ? 0 : stats.size,
          modified: stats.mtime.getTime(),
          isDirectory: isDirectory,
          score: calculateScore(itemName, query),
        }

        results.push(result)
      }

      // Türkçe: Alt klasörlerde ara
      if (item.isDirectory() && currentDepth < maxDepth) {
        try {
          searchDirectory(itemPath, query, filters, results, currentDepth + 1, maxDepth)
        } catch (error) {
          // Türkçe: Erişim hatalarını sessizce atla
          continue
        }
      }
    }
  } catch (error) {
    // Türkçe: Dizin okuma hatalarını sessizce atla
    return
  }
}

function isSystemPath(filePath) {
  const systemPaths = [
    "node_modules",
    ".git",
    ".vscode",
    "AppData",
    "Library",
    "System",
    "$Recycle.Bin",
    "System Volume Information",
  ]

  return systemPaths.some((sysPath) => filePath.includes(sysPath))
}

function matchesFilters(filePath, isDirectory, filters) {
  // Türkçe: Tip filtresi
  if (filters.type && filters.type !== "All") {
    switch (filters.type) {
      case "Files":
        if (isDirectory) return false
        break
      case "Folders":
        if (!isDirectory) return false
        break
      case "Images":
        if (isDirectory) return false
        const imageExts = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp"]
        if (!imageExts.includes(path.extname(filePath).toLowerCase())) return false
        break
      case "Documents":
        if (isDirectory) return false
        const docExts = [".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt"]
        if (!docExts.includes(path.extname(filePath).toLowerCase())) return false
        break
      case "Videos":
        if (isDirectory) return false
        const videoExts = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm"]
        if (!videoExts.includes(path.extname(filePath).toLowerCase())) return false
        break
      case "Audio":
        if (isDirectory) return false
        const audioExts = [".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma"]
        if (!audioExts.includes(path.extname(filePath).toLowerCase())) return false
        break
    }
  }

  return true
}

function calculateScore(itemName, query) {
  let score = 0

  // Türkçe: Tam eşleşme
  if (itemName === query) {
    score += 100
  }
  // Türkçe: Başlangıç eşleşmesi
  else if (itemName.startsWith(query)) {
    score += 50
  }
  // Türkçe: İçerik eşleşmesi
  else if (itemName.includes(query)) {
    score += 25
  }

  // Türkçe: Dosya uzantısına göre bonus
  const ext = path.extname(itemName).toLowerCase()
  const commonExts = [".txt", ".pdf", ".doc", ".docx", ".jpg", ".png", ".mp4", ".mp3"]
  if (commonExts.includes(ext)) {
    score += 5
  }

  return score
}

// Türkçe: Dosya/klasör aç
ipcMain.handle("open-file", async (event, filePath) => {
  try {
    await shell.openPath(filePath)
    return { success: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

// Türkçe: Dosyayı klasörde göster
ipcMain.handle("show-in-folder", async (event, filePath) => {
  try {
    shell.showItemInFolder(filePath)
    return { success: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

// Türkçe: Sistem komutu çalıştır
ipcMain.handle("run-command", async (event, command) => {
  return new Promise((resolve) => {
    exec(command, (error, stdout, stderr) => {
      resolve({
        success: !error,
        output: stdout || stderr || error?.message || "",
        error: error?.message,
      })
    })
  })
})

// Türkçe: Pano geçmişi
const clipboardHistory = []
const { clipboard } = require("electron")

setInterval(() => {
  const currentText = clipboard.readText()
  if (currentText && currentText.length > 0) {
    const lastItem = clipboardHistory[clipboardHistory.length - 1]
    if (!lastItem || lastItem.text !== currentText) {
      clipboardHistory.push({
        text: currentText,
        timestamp: Date.now(),
        type: "text",
      })

      if (clipboardHistory.length > 100) {
        clipboardHistory.shift()
      }
    }
  }
}, 1000)

ipcMain.handle("get-clipboard-history", () => {
  return clipboardHistory.slice().reverse()
})

ipcMain.handle("copy-to-clipboard", (event, text) => {
  clipboard.writeText(text)
  return { success: true }
})
