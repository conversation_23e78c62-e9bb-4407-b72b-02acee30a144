// Türkçe: Ana uygulama sınıfı - Tamamen yeniden yazıldı

class ModularApp {
  constructor() {
    this.currentModule = "home"
    this.modules = new Map()
    this.moduleInstances = new Map()
    this.isResizing = false
    this.geminiApiKey = localStorage.getItem("gemini_api_key") || ""

    // DOM elementleri
    this.elements = {
      mainInput: document.getElementById("main-input"),
      currentModuleBtn: document.getElementById("current-module-btn"),
      currentModuleIcon: document.getElementById("current-module-icon"),
      currentModuleName: document.getElementById("current-module-name"),
      moduleDropdown: document.getElementById("module-dropdown"),
      filtersContainer: document.getElementById("filters-container"),
      mainContent: document.getElementById("main-content"),
      statusText: document.getElementById("status-text"),
      selectionInfo: document.getElementById("selection-info"),
    }

    // Komut tanımları
    this.commands = {
      "!home": "home",
      "!fs": "file-search",
      "!web": "web-search",
      "!cb": "clipboard",
      "!clip": "clipboard",
      "!calc": "calculator",
      "!settings": "settings",
      "!ai": "ai",
    }

    this.init()
  }

  async init() {
    this.setupEventListeners()
    this.setupWindowControls()
    this.setupModalEvents()
    this.loadDefaultModules()
    this.renderModuleDropdown()
    this.loadModule("home")
    this.updateStatus("Ready")
  }

  setupEventListeners() {
    // Ana input olayları
    this.elements.mainInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        this.handleInput(e.target.value)
      }
    })

    // Modül dropdown
    this.elements.currentModuleBtn.addEventListener("click", () => {
      this.toggleModuleDropdown()
    })

    // Dropdown dışına tıklama
    document.addEventListener("click", (e) => {
      if (!e.target.closest(".module-selector")) {
        this.elements.moduleDropdown.classList.remove("show")
      }
    })

    // Resize handle
    document.addEventListener("mousedown", (e) => {
      if (e.target.classList.contains("resize-handle")) {
        this.startResize(e)
      }
    })

    document.addEventListener("mousemove", (e) => {
      if (this.isResizing) {
        this.handleResize(e)
      }
    })

    document.addEventListener("mouseup", () => {
      this.isResizing = false
      document.body.style.cursor = "default"
    })
  }

  setupWindowControls() {
    document.getElementById("minimize-btn")?.addEventListener("click", () => {
      window.electronAPI?.minimizeWindow()
    })

    document.getElementById("maximize-btn")?.addEventListener("click", () => {
      window.electronAPI?.maximizeWindow()
    })

    document.getElementById("close-btn")?.addEventListener("click", () => {
      window.electronAPI?.closeWindow()
    })
  }

  setupModalEvents() {
    const overlay = document.getElementById("api-modal-overlay")
    const closeBtn = document.getElementById("modal-close")
    const cancelBtn = document.getElementById("modal-cancel")
    const saveBtn = document.getElementById("modal-save")

    closeBtn?.addEventListener("click", () => this.hideApiModal())
    cancelBtn?.addEventListener("click", () => this.hideApiModal())
    saveBtn?.addEventListener("click", () => this.saveApiKey())

    overlay?.addEventListener("click", (e) => {
      if (e.target === overlay) {
        this.hideApiModal()
      }
    })
  }

  showApiModal() {
    const overlay = document.getElementById("api-modal-overlay")
    const input = document.getElementById("gemini-api-key")
    if (input) input.value = this.geminiApiKey
    overlay?.classList.add("show")
  }

  hideApiModal() {
    const overlay = document.getElementById("api-modal-overlay")
    overlay?.classList.remove("show")
  }

  saveApiKey() {
    const input = document.getElementById("gemini-api-key")
    if (input) {
      this.geminiApiKey = input.value.trim()
      localStorage.setItem("gemini_api_key", this.geminiApiKey)
      this.hideApiModal()
      this.updateStatus("API key saved")
    }
  }

  loadDefaultModules() {
    const defaultModules = [
      {
        id: "home",
        name: "Ana Sayfa",
        icon: "fas fa-home",
        layout: "single",
      },
      {
        id: "file-search",
        name: "Dosya Arama",
        icon: "fas fa-search",
        layout: "two-panel",
        filters: [
          {
            id: "location",
            type: "select",
            label: "Konum",
            options: ["Tümü", "Masaüstü", "Belgeler", "İndirilenler", "Resimler", "Videolar", "Müzik"],
          },
          {
            id: "file_type",
            type: "select",
            label: "Dosya Tipi",
            options: ["Tümü", "Klasörler", "Uygulamalar", "Dökümanlar", "Resimler", "Videolar", "Müzik"],
          },
        ],
      },
      {
        id: "web-search",
        name: "Web Arama",
        icon: "fas fa-globe",
        layout: "two-panel",
        filters: [
          {
            id: "engine",
            type: "select",
            label: "Arama Motoru",
            options: ["Google", "Bing", "DuckDuckGo"],
          },
        ],
      },
      {
        id: "clipboard",
        name: "Pano Geçmişi",
        icon: "fas fa-clipboard",
        layout: "two-panel",
        filters: [
          {
            id: "content_type",
            type: "select",
            label: "İçerik Tipi",
            options: ["Tümü", "Metin", "URL", "Email"],
          },
        ],
      },
      {
        id: "calculator",
        name: "Hesap Makinesi",
        icon: "fas fa-calculator",
        layout: "single",
      },
      {
        id: "settings",
        name: "Ayarlar",
        icon: "fas fa-cog",
        layout: "single",
      },
      {
        id: "ai",
        name: "AI Asistan",
        icon: "fas fa-brain",
        layout: "single",
        filters: [
          {
            id: "mode",
            type: "radio",
            label: "Mod",
            options: ["chat", "image"],
            default: "chat",
          },
        ],
      },
    ]

    defaultModules.forEach((module) => {
      this.modules.set(module.id, module)
    })
  }

  renderModuleDropdown() {
    const dropdown = this.elements.moduleDropdown
    if (!dropdown) return

    dropdown.innerHTML = ""

    this.modules.forEach((module, id) => {
      const item = document.createElement("button")
      item.className = "module-dropdown-item"
      item.innerHTML = `
        <i class="${module.icon}"></i>
        <span>${module.name}</span>
      `
      item.addEventListener("click", () => {
        this.loadModule(id)
        this.elements.moduleDropdown.classList.remove("show")
      })
      dropdown.appendChild(item)
    })
  }

  toggleModuleDropdown() {
    this.elements.moduleDropdown?.classList.toggle("show")
  }

  handleInput(value) {
    const trimmedValue = value.trim()

    // Komut kontrolü
    const command = this.checkCommand(trimmedValue)
    if (command) {
      this.loadModule(command)
      this.elements.mainInput.value = ""
      return
    }

    // Mevcut modüle göre işlem
    const moduleInstance = this.moduleInstances.get(this.currentModule)
    if (moduleInstance && moduleInstance.handleQuery) {
      moduleInstance.handleQuery(trimmedValue)
      this.elements.mainInput.value = ""
    } else {
      // Bilinmeyen komut - AI'ya gönder
      this.loadModule("ai")
      setTimeout(() => {
        const aiInstance = this.moduleInstances.get("ai")
        if (aiInstance && aiInstance.handleQuery) {
          aiInstance.handleQuery(trimmedValue)
        }
      }, 100)
    }
  }

  checkCommand(input) {
    const words = input.toLowerCase().split(" ")
    const firstWord = words[0]
    return this.commands[firstWord] || null
  }

  async loadModule(moduleId) {
    if (!this.modules.has(moduleId)) {
      this.updateStatus(`Module ${moduleId} not found`)
      return
    }

    // Önceki modülü durdur
    const prevInstance = this.moduleInstances.get(this.currentModule)
    if (prevInstance && prevInstance.stop) {
      prevInstance.stop()
    }

    const module = this.modules.get(moduleId)
    this.currentModule = moduleId

    // UI güncelle
    this.updateCurrentModuleDisplay(module)
    this.renderFilters(module.filters || [])
    this.renderModuleContent(module)

    // Modül instance'ını oluştur ve çalıştır
    this.createModuleInstance(moduleId)

    this.updateStatus(`${module.name} yüklendi`)
  }

  createModuleInstance(moduleId) {
    // Önceki instance'ı temizle
    if (this.moduleInstances.has(moduleId)) {
      const prevInstance = this.moduleInstances.get(moduleId)
      if (prevInstance.stop) prevInstance.stop()
    }

    // Yeni instance oluştur
    let instance

    switch (moduleId) {
      case "home":
        instance = this.createHomeModule()
        break
      case "ai":
        instance = this.createAIModule()
        break
      case "file-search":
        instance = this.createFileSearchModule()
        break
      case "web-search":
        instance = this.createWebSearchModule()
        break
      case "clipboard":
        instance = this.createClipboardModule()
        break
      case "calculator":
        instance = this.createCalculatorModule()
        break
      case "settings":
        instance = this.createSettingsModule()
        break
      default:
        console.warn(`Unknown module: ${moduleId}`)
        return
    }

    if (instance) {
      this.moduleInstances.set(moduleId, instance)
      if (instance.run) {
        instance.run()
      }
    }
  }

  createHomeModule() {
    const contentElement = document.getElementById("single-panel") || this.elements.mainContent

    return {
      run() {
        contentElement.innerHTML = `
          <div style="padding: 40px; text-align: center; height: 100%; overflow-y: auto;">
            <i class="fas fa-home" style="font-size: 64px; color: var(--accent-color); margin-bottom: 24px;"></i>
            <h1 style="color: var(--text-primary); margin-bottom: 16px;">Modüler Masaüstü Uygulamasına Hoş Geldiniz</h1>
            <p style="color: var(--text-secondary); margin-bottom: 32px; max-width: 600px; margin-left: auto; margin-right: auto;">
              Komutları kullanın: !fs dosya arama, !web web arama, !ai AI asistan, !clip pano geçmişi, !calc hesap makinesi
            </p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; max-width: 1000px; margin: 0 auto;">
              <div class="feature-card" onclick="window.app.loadModule('file-search')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-search" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">Dosya Arama</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Bilgisayarınızdaki dosya ve klasörleri hızlıca bulun</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!fs</code>
              </div>
              
              <div class="feature-card" onclick="window.app.loadModule('ai')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-brain" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">AI Asistan</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Gemini AI ile sohbet edin veya resim oluşturun</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!ai</code>
              </div>
              
              <div class="feature-card" onclick="window.app.loadModule('web-search')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-globe" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">Web Arama</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">İnternette arama yapın ve sonuçları görüntüleyin</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!web</code>
              </div>
              
              <div class="feature-card" onclick="window.app.loadModule('clipboard')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-clipboard" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">Pano Geçmişi</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Kopyaladığınız metinleri görüntüleyin ve yönetin</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!clip</code>
              </div>
              
              <div class="feature-card" onclick="window.app.loadModule('calculator')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-calculator" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">Hesap Makinesi</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Gelişmiş hesap makinesi ve matematiksel işlemler</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!calc</code>
              </div>
              
              <div class="feature-card" onclick="window.app.loadModule('settings')" style="background: var(--bg-secondary); padding: 24px; border-radius: 12px; border: 1px solid var(--border-color); cursor: pointer; transition: all 0.3s ease;">
                <i class="fas fa-cog" style="font-size: 32px; color: var(--accent-color); margin-bottom: 16px;"></i>
                <h3 style="color: var(--text-primary); margin-bottom: 8px;">Ayarlar</h3>
                <p style="color: var(--text-muted); font-size: 14px; margin-bottom: 12px;">Uygulama ayarları ve konfigürasyonları</p>
                <code style="background: var(--bg-tertiary); padding: 4px 8px; border-radius: 4px; font-size: 12px; color: var(--accent-color);">!settings</code>
              </div>
            </div>
          </div>
        `

        // Hover efektleri
        const style = document.createElement("style")
        style.textContent = `
          .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px var(--shadow);
            border-color: var(--accent-color);
          }
        `
        document.head.appendChild(style)
      },

      stop() {},

      handleQuery(query) {
        window.app.loadModule("ai")
        setTimeout(() => {
          const aiInstance = window.app.moduleInstances.get("ai")
          if (aiInstance && aiInstance.handleQuery) {
            aiInstance.handleQuery(query)
          }
        }, 100)
      },
    }
  }

  createAIModule() {
    const contentElement = document.getElementById("single-panel") || this.elements.mainContent
    const chatHistory = JSON.parse(localStorage.getItem("ai_chat_history") || "[]")
    let isProcessing = false
    let currentMode = "chat"

    return {
      run() {
        this.renderChatInterface()
        this.setupEventListeners()
        window.app.updateStatus("AI Asistan hazır")
      },

      stop() {
        localStorage.setItem("ai_chat_history", JSON.stringify(chatHistory))
      },

      handleQuery(query) {
        if (query.trim()) {
          this.sendMessage(query.trim())
        }
      },

      renderChatInterface() {
        contentElement.innerHTML = `
          <div class="ai-chat-container" style="height: 100%; display: flex; flex-direction: column;">
            <div class="chat-header" style="padding: 16px; border-bottom: 1px solid var(--border-color); flex-shrink: 0;">
              <div class="chat-title" style="display: flex; align-items: center; gap: 12px;">
                <i class="fas fa-brain" style="color: var(--accent-color);"></i>
                <h2 style="margin: 0; color: var(--text-primary);">AI Asistan</h2>
              </div>
            </div>
            
            <div class="chat-messages" id="chat-messages" style="flex: 1; overflow-y: auto; padding: 16px;">
              ${chatHistory.length === 0 ? this.getWelcomeMessage() : ""}
            </div>
            
            <div class="chat-input-container" style="padding: 16px; border-top: 1px solid var(--border-color); flex-shrink: 0;">
              <div class="input-wrapper" style="display: flex; gap: 8px; align-items: flex-end;">
                <textarea 
                  id="chat-input" 
                  placeholder="AI'ya bir soru sorun veya sohbet edin..."
                  rows="1"
                  style="flex: 1; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); color: var(--text-primary); resize: none; font-family: inherit;"
                ></textarea>
                <button id="send-btn" class="send-btn" style="padding: 12px 16px; background: var(--accent-color); color: white; border: none; border-radius: 8px; cursor: pointer;">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
              <div class="input-footer" style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 12px; color: var(--text-muted);">
                <span class="model-info">Model: gemini-2.0-flash-exp</span>
                <span class="mode-info">Mod: <span id="current-mode">Chat</span></span>
              </div>
            </div>
          </div>
        `

        this.renderChatHistory()
      },

      getWelcomeMessage() {
        return `
          <div class="welcome-message" style="text-align: center; padding: 40px 20px;">
            <div class="ai-avatar" style="width: 64px; height: 64px; background: var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px;">
              <i class="fas fa-robot" style="font-size: 32px; color: white;"></i>
            </div>
            <div class="welcome-content">
              <h3 style="color: var(--text-primary); margin-bottom: 16px;">AI Asistan'a Hoş Geldiniz!</h3>
              <p style="color: var(--text-secondary); margin-bottom: 32px; max-width: 500px; margin-left: auto; margin-right: auto;">
                Ben Gemini AI'yım. Size yardımcı olmak için buradayım. Sorularınızı sorun, kod yazımında yardım isteyin, yaratıcı projeler için fikir alın veya sadece sohbet edin!
              </p>
              <div class="example-prompts" style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
                <div class="prompt-item" data-prompt="JavaScript ile bir hesap makinesi nasıl yapılır?" style="background: var(--bg-secondary); padding: 8px 16px; border-radius: 20px; cursor: pointer; border: 1px solid var(--border-color);">
                  💻 Kod yardımı
                </div>
                <div class="prompt-item" data-prompt="Bana kısa bir hikaye yaz" style="background: var(--bg-secondary); padding: 8px 16px; border-radius: 20px; cursor: pointer; border: 1px solid var(--border-color);">
                  ✍️ Yaratıcı yazım
                </div>
                <div class="prompt-item" data-prompt="Python öğrenmek için en iyi kaynaklar neler?" style="background: var(--bg-secondary); padding: 8px 16px; border-radius: 20px; cursor: pointer; border: 1px solid var(--border-color);">
                  📚 Öğrenme
                </div>
              </div>
            </div>
          </div>
        `
      },

      setupEventListeners() {
        const chatInput = document.getElementById("chat-input")
        const sendBtn = document.getElementById("send-btn")

        if (!chatInput || !sendBtn) return

        // Metin alanı olayları
        chatInput.addEventListener("input", (e) => {
          this.handleInputChange(e.target.value)
        })

        chatInput.addEventListener("keydown", (e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault()
            this.sendMessage()
          }
        })

        // Gönder butonu
        sendBtn.addEventListener("click", () => {
          this.sendMessage()
        })

        // Örnek prompt'lara tıklama
        document.addEventListener("click", (e) => {
          if (e.target.classList.contains("prompt-item")) {
            const prompt = e.target.dataset.prompt
            chatInput.value = prompt
            this.handleInputChange(prompt)
            chatInput.focus()
          }
        })

        // Mod değişikliği dinle
        document.addEventListener("change", (e) => {
          if (e.target.name === "mode") {
            currentMode = e.target.value
            const modeSpan = document.getElementById("current-mode")
            if (modeSpan) {
              modeSpan.textContent = e.target.value === "chat" ? "Chat" : "Image"
            }
          }
        })
      },

      handleInputChange(value) {
        const sendBtn = document.getElementById("send-btn")
        if (sendBtn) {
          sendBtn.disabled = !value.trim() || isProcessing
        }

        // Textarea yüksekliğini otomatik ayarla
        const textarea = document.getElementById("chat-input")
        if (textarea) {
          textarea.style.height = "auto"
          textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px"
        }
      },

      async sendMessage(message = null) {
        const chatInput = document.getElementById("chat-input")
        const messageText = message || (chatInput ? chatInput.value.trim() : "")

        if (!messageText || isProcessing) return

        const apiKey = window.app.geminiApiKey
        if (!apiKey) {
          window.app.showApiModal()
          return
        }

        // Kullanıcı mesajını ekle
        this.addMessage("user", messageText)
        if (chatInput) {
          chatInput.value = ""
          this.handleInputChange("")
        }

        // AI yanıtını al
        await this.getAIResponse(messageText, apiKey)
      },

      async getAIResponse(message, apiKey) {
        isProcessing = true
        window.app.updateStatus("AI düşünüyor...")

        // Yükleniyor mesajı ekle
        const loadingId = this.addMessage("assistant", "", true)

        try {
          const response = await this.callGeminiAPI(message, apiKey)
          this.updateMessage(loadingId, response)
          window.app.updateStatus("AI yanıtladı")
        } catch (error) {
          console.error("AI API hatası:", error)
          this.updateMessage(
            loadingId,
            "Üzgünüm, bir hata oluştu. Lütfen API anahtarınızı kontrol edin ve tekrar deneyin.",
          )
          window.app.updateStatus("AI hatası")
        } finally {
          isProcessing = false
          const chatInput = document.getElementById("chat-input")
          if (chatInput) {
            this.handleInputChange(chatInput.value)
          }
        }
      },

      async callGeminiAPI(message, apiKey) {
        // DOĞRU API endpoint ve format
        const response = await fetch(
          "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-goog-api-key": apiKey,
            },
            body: JSON.stringify({
              contents: [
                {
                  parts: [
                    {
                      text: message,
                    },
                  ],
                },
              ],
              generationConfig: {
                temperature: 0.7,
                maxOutputTokens: 2048,
              },
            }),
          },
        )

        if (!response.ok) {
          throw new Error(`API hatası: ${response.status}`)
        }

        const data = await response.json()

        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
          return data.candidates[0].content.parts[0].text
        } else {
          throw new Error("Geçersiz API yanıtı")
        }
      },

      addMessage(role, content, isLoading = false) {
        const messageId = Date.now() + Math.random()
        const message = {
          id: messageId,
          role,
          content,
          timestamp: Date.now(),
          isLoading,
        }

        chatHistory.push(message)
        this.renderMessage(message)
        this.scrollToBottom()

        return messageId
      },

      updateMessage(messageId, content) {
        const message = chatHistory.find((m) => m.id === messageId)
        if (message) {
          message.content = content
          message.isLoading = false

          const messageElement = document.querySelector(`[data-message-id="${messageId}"]`)
          if (messageElement) {
            const contentElement = messageElement.querySelector(".message-content")
            if (contentElement) {
              contentElement.innerHTML = this.formatMessageContent(content)
            }
          }
        }
      },

      renderMessage(message) {
        const messagesContainer = document.getElementById("chat-messages")
        if (!messagesContainer) return

        // Hoş geldin mesajını kaldır
        const welcomeMessage = messagesContainer.querySelector(".welcome-message")
        if (welcomeMessage) {
          welcomeMessage.remove()
        }

        const messageElement = document.createElement("div")
        messageElement.className = `message ${message.role}-message`
        messageElement.dataset.messageId = message.id
        messageElement.style.cssText = `
          display: flex;
          gap: 12px;
          margin-bottom: 16px;
          ${message.role === "user" ? "flex-direction: row-reverse;" : ""}
        `

        messageElement.innerHTML = `
          <div class="message-avatar" style="width: 32px; height: 32px; border-radius: 50%; background: ${message.role === "user" ? "var(--accent-color)" : "var(--bg-tertiary)"}; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
            <i class="fas ${message.role === "user" ? "fa-user" : "fa-robot"}" style="font-size: 14px; color: ${message.role === "user" ? "white" : "var(--text-primary)"};"></i>
          </div>
          <div class="message-bubble" style="max-width: 70%; background: ${message.role === "user" ? "var(--accent-color)" : "var(--bg-secondary)"}; padding: 12px 16px; border-radius: 16px; ${message.role === "user" ? "border-bottom-right-radius: 4px;" : "border-bottom-left-radius: 4px;"}">
            <div class="message-content" style="color: ${message.role === "user" ? "white" : "var(--text-primary)"}; line-height: 1.5;">
              ${message.isLoading ? this.getLoadingContent() : this.formatMessageContent(message.content)}
            </div>
            <div class="message-time" style="font-size: 11px; color: ${message.role === "user" ? "rgba(255,255,255,0.7)" : "var(--text-muted)"}; margin-top: 4px;">
              ${new Date(message.timestamp).toLocaleTimeString("tr-TR", { hour: "2-digit", minute: "2-digit" })}
            </div>
          </div>
        `

        messagesContainer.appendChild(messageElement)
      },

      getLoadingContent() {
        return `
          <div class="loading-dots" style="display: flex; gap: 4px;">
            <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; animation: loading 1.4s infinite ease-in-out;"></span>
            <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; animation: loading 1.4s infinite ease-in-out 0.2s;"></span>
            <span style="width: 6px; height: 6px; background: currentColor; border-radius: 50%; animation: loading 1.4s infinite ease-in-out 0.4s;"></span>
          </div>
          <style>
            @keyframes loading {
              0%, 80%, 100% { opacity: 0.3; transform: scale(0.8); }
              40% { opacity: 1; transform: scale(1); }
            }
          </style>
        `
      },

      formatMessageContent(content) {
        return content
          .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
          .replace(/\*(.*?)\*/g, "<em>$1</em>")
          .replace(
            /`(.*?)`/g,
            "<code style='background: rgba(255,255,255,0.1); padding: 2px 4px; border-radius: 3px;'>$1</code>",
          )
          .replace(
            /```([\s\S]*?)```/g,
            "<pre style='background: rgba(255,255,255,0.1); padding: 8px; border-radius: 6px; overflow-x: auto; margin: 8px 0;'><code>$1</code></pre>",
          )
          .replace(/\n/g, "<br>")
      },

      renderChatHistory() {
        const messagesContainer = document.getElementById("chat-messages")
        if (!messagesContainer) return

        messagesContainer.innerHTML = ""

        if (chatHistory.length === 0) {
          messagesContainer.innerHTML = this.getWelcomeMessage()
          return
        }

        chatHistory.forEach((message) => {
          this.renderMessage(message)
        })

        this.scrollToBottom()
      },

      scrollToBottom() {
        const messagesContainer = document.getElementById("chat-messages")
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight
        }
      },
    }
  }

  // Diğer modül oluşturucuları da benzer şekilde implement edilecek
  createFileSearchModule() {
    return {
      run() {
        const leftPanel = document.getElementById("left-panel")
        const rightPanel = document.getElementById("right-panel")

        if (leftPanel) {
          leftPanel.innerHTML = `
            <div style="padding: 16px;">
              <h3 style="color: var(--text-primary); margin-bottom: 16px;">Dosya Arama</h3>
              <p style="color: var(--text-secondary);">Arama yapmak için yukarıdaki kutucuğa dosya adı yazın.</p>
            </div>
          `
        }

        if (rightPanel) {
          rightPanel.innerHTML = `
            <div style="padding: 16px; text-align: center;">
              <i class="fas fa-search" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
              <h3 style="color: var(--text-primary);">Dosya Seçin</h3>
              <p style="color: var(--text-secondary);">Bir dosya seçin ve detaylarını görün</p>
            </div>
          `
        }
      },
      stop() {},
      handleQuery(query) {
        console.log("File search:", query)
        // Gerçek dosya arama implementasyonu
      },
    }
  }

  createWebSearchModule() {
    return {
      run() {
        const leftPanel = document.getElementById("left-panel")
        const rightPanel = document.getElementById("right-panel")

        if (leftPanel) {
          leftPanel.innerHTML = `
            <div style="padding: 16px;">
              <h3 style="color: var(--text-primary); margin-bottom: 16px;">Web Arama</h3>
              <p style="color: var(--text-secondary);">İnternette arama yapmak için yukarıdaki kutucuğa arama terimini yazın.</p>
            </div>
          `
        }

        if (rightPanel) {
          rightPanel.innerHTML = `
            <div style="padding: 16px; text-align: center;">
              <i class="fas fa-globe" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
              <h3 style="color: var(--text-primary);">Web Sonucu Seçin</h3>
              <p style="color: var(--text-secondary);">Bir arama sonucu seçin ve detaylarını görün</p>
            </div>
          `
        }
      },
      stop() {},
      handleQuery(query) {
        console.log("Web search:", query)
        // Gerçek web arama implementasyonu
      },
    }
  }

  createClipboardModule() {
    return {
      run() {
        const leftPanel = document.getElementById("left-panel")
        const rightPanel = document.getElementById("right-panel")

        if (leftPanel) {
          leftPanel.innerHTML = `
            <div style="padding: 16px;">
              <h3 style="color: var(--text-primary); margin-bottom: 16px;">Pano Geçmişi</h3>
              <p style="color: var(--text-secondary);">Kopyaladığınız metinler burada görünecek.</p>
            </div>
          `
        }

        if (rightPanel) {
          rightPanel.innerHTML = `
            <div style="padding: 16px; text-align: center;">
              <i class="fas fa-clipboard" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
              <h3 style="color: var(--text-primary);">Pano Öğesi Seçin</h3>
              <p style="color: var(--text-secondary);">Bir pano öğesi seçin ve detaylarını görün</p>
            </div>
          `
        }
      },
      stop() {},
      handleQuery(query) {
        console.log("Clipboard search:", query)
        // Gerçek pano arama implementasyonu
      },
    }
  }

  createCalculatorModule() {
    return {
      run() {
        const contentElement = document.getElementById("single-panel") || this.elements.mainContent

        contentElement.innerHTML = `
          <div style="padding: 40px; text-align: center;">
            <i class="fas fa-calculator" style="font-size: 64px; color: var(--accent-color); margin-bottom: 24px;"></i>
            <h2 style="color: var(--text-primary); margin-bottom: 16px;">Hesap Makinesi</h2>
            <p style="color: var(--text-secondary);">Hesap makinesi modülü yakında eklenecek.</p>
          </div>
        `
      },
      stop() {},
      handleQuery(query) {
        console.log("Calculator:", query)
        // Gerçek hesap makinesi implementasyonu
      },
    }
  }

  createSettingsModule() {
    return {
      run() {
        const contentElement = document.getElementById("single-panel") || this.elements.mainContent

        contentElement.innerHTML = `
          <div style="padding: 40px; text-align: center;">
            <i class="fas fa-cog" style="font-size: 64px; color: var(--accent-color); margin-bottom: 24px;"></i>
            <h2 style="color: var(--text-primary); margin-bottom: 16px;">Ayarlar</h2>
            <p style="color: var(--text-secondary);">Ayarlar modülü yakında eklenecek.</p>
          </div>
        `
      },
      stop() {},
      handleQuery(query) {
        console.log("Settings:", query)
        // Gerçek ayarlar implementasyonu
      },
    }
  }

  updateCurrentModuleDisplay(module) {
    if (this.elements.currentModuleIcon) {
      this.elements.currentModuleIcon.className = module.icon
    }
    if (this.elements.currentModuleName) {
      this.elements.currentModuleName.textContent = module.name
    }
  }

  renderFilters(filters) {
    const container = this.elements.filtersContainer
    if (!container) return

    container.innerHTML = ""

    filters.forEach((filter) => {
      const filterGroup = document.createElement("div")
      filterGroup.className = "filter-group"

      if (filter.type === "select") {
        filterGroup.innerHTML = `
          <label class="filter-label">${filter.label}:</label>
          <select class="filter-select" data-filter="${filter.id}">
            ${filter.options.map((option) => `<option value="${option}">${option}</option>`).join("")}
          </select>
        `
      } else if (filter.type === "radio") {
        filterGroup.innerHTML = `
          <label class="filter-label">${filter.label}:</label>
          <div class="filter-radio-group">
            ${filter.options
              .map(
                (option) => `
              <label class="filter-radio">
                <input type="radio" name="${filter.id}" value="${option}" 
                       ${option === (filter.default || filter.options[0]) ? "checked" : ""}>
                <span>${option}</span>
              </label>
            `,
              )
              .join("")}
          </div>
        `
      }

      container.appendChild(filterGroup)
    })

    // AI modu için özel işlem
    if (this.currentModule === "ai") {
      const apiBtn = document.createElement("button")
      apiBtn.className = "api-settings-btn"
      apiBtn.innerHTML = '<i class="fas fa-key"></i> API'
      apiBtn.title = "API Settings"
      apiBtn.addEventListener("click", () => this.showApiModal())
      container.appendChild(apiBtn)
    }
  }

  renderModuleContent(module) {
    const container = this.elements.mainContent
    if (!container) return

    if (module.layout === "two-panel") {
      container.innerHTML = `
        <div class="two-panel-layout">
          <div class="left-panel" id="left-panel">
            <div class="list-container" id="list-container">
              <div class="loading-state">
                <div class="loading-spinner">
                  <i class="fas fa-spinner fa-spin"></i>
                </div>
                <p>Arama için hazır...</p>
              </div>
            </div>
          </div>
          <div class="resize-handle"></div>
          <div class="right-panel" id="right-panel">
            <div class="preview-container" id="preview-container">
              <div class="preview-placeholder">
                <i class="${module.icon}"></i>
                <h3>${module.name}</h3>
                <p>Bir öğe seçin ve önizlemesini görün</p>
              </div>
            </div>
          </div>
        </div>
      `
    } else {
      container.innerHTML = `
        <div class="single-panel-layout" id="single-panel">
          <div class="preview-container">
            <div class="preview-placeholder">
              <i class="${module.icon}"></i>
              <h3>${module.name}</h3>
              <p>Modül içeriği yüklenecek...</p>
            </div>
          </div>
        </div>
      `
    }
  }

  getActiveFilters() {
    const filters = {}
    const filterElements = this.elements.filtersContainer?.querySelectorAll("select, input:checked") || []

    filterElements.forEach((element) => {
      const filterId = element.dataset.filter || element.name
      if (filterId) {
        filters[filterId] = element.value
      }
    })

    return filters
  }

  startResize(e) {
    this.isResizing = true
    this.resizeStartX = e.clientX
    this.resizeStartY = e.clientY
    document.body.style.cursor = e.target.classList.contains("resize-handle") ? "col-resize" : "row-resize"
    e.preventDefault()
  }

  handleResize(e) {
    if (!this.isResizing) return

    const container = document.querySelector(".two-panel-layout")
    if (!container) return

    const leftPanel = container.querySelector(".left-panel")
    const rightPanel = container.querySelector(".right-panel")

    if (window.innerWidth <= 768) {
      const deltaY = e.clientY - this.resizeStartY
      const containerHeight = container.offsetHeight
      const currentHeight = leftPanel.offsetHeight
      const newHeight = Math.max(100, Math.min(containerHeight - 100, currentHeight + deltaY))

      leftPanel.style.height = newHeight + "px"
      rightPanel.style.height = containerHeight - newHeight - 4 + "px"
    } else {
      const deltaX = e.clientX - this.resizeStartX
      const containerWidth = container.offsetWidth
      const currentWidth = leftPanel.offsetWidth
      const newWidth = Math.max(200, Math.min(containerWidth * 0.6, currentWidth + deltaX))

      leftPanel.style.width = newWidth + "px"
    }

    this.resizeStartX = e.clientX
    this.resizeStartY = e.clientY
  }

  updateStatus(text) {
    if (this.elements.statusText) {
      this.elements.statusText.textContent = text
      setTimeout(() => {
        if (this.elements.statusText.textContent === text) {
          this.elements.statusText.textContent = "Ready"
        }
      }, 3000)
    }
  }

  updateSelection(text) {
    if (this.elements.selectionInfo) {
      this.elements.selectionInfo.textContent = text
    }
  }
}

// Uygulama başlat
document.addEventListener("DOMContentLoaded", () => {
  window.app = new ModularApp()
})
