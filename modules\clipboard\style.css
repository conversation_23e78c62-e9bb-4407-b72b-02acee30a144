/* Türkçe: <PERSON><PERSON> g<PERSON>ç<PERSON>şi modü<PERSON>ü stilleri */

.clipboard-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.clipboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.clipboard-header h3 {
  margin: 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clipboard-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.clipboard-stats {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.clipboard-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.no-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.no-items i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-items small {
  margin-top: 0.5rem;
  opacity: 0.7;
}

.clipboard-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
}

.clipboard-item:hover {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

.clipboard-item.selected {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.clipboard-item.selected .item-meta {
  color: rgba(255, 255, 255, 0.8);
}

.item-icon {
  font-size: 1.25rem;
  width: 2rem;
  text-align: center;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-preview {
  font-weight: 500;
  margin-bottom: 0.375rem;
  line-height: 1.4;
  word-wrap: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.item-meta {
  display: flex;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: var(--text-muted);
  flex-wrap: wrap;
}

.item-type {
  background: var(--bg-tertiary);
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-weight: 500;
}

.clipboard-item.selected .item-type {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.item-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.clipboard-item:hover .item-actions,
.clipboard-item.selected .item-actions {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.action-btn:hover {
  background: var(--accent-color);
  color: white;
}

.clipboard-item.selected .action-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.clipboard-item.selected .action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.delete-btn:hover {
  background: var(--danger-color) !important;
}

/* Türkçe: Önizleme stilleri */
.clipboard-preview {
  height: 100%;
  overflow-y: auto;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
}

.preview-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.preview-header {
  display: flex;
  align-items: flex-start;
  padding: 2rem;
  border-bottom: 1px solid var(--border-color);
  gap: 1.5rem;
}

.preview-icon {
  font-size: 2.5rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.preview-info h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.preview-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.preview-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-content {
  padding: 2rem;
}

.content-display {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  max-height: 400px;
  overflow-y: auto;
}

.text-content {
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.code-block {
  background: var(--bg-tertiary);
  border-radius: 6px;
  padding: 1rem;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
}

.url-link,
.email-link {
  color: var(--accent-color);
  text-decoration: none;
  word-break: break-all;
}

.url-link:hover,
.email-link:hover {
  text-decoration: underline;
}

.preview-actions {
  padding: 2rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
}

.preview-action-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.preview-action-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.preview-action-btn.primary {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.preview-action-btn.primary:hover {
  background: var(--accent-hover);
}

/* Türkçe: Scrollbar stilleri */
.clipboard-list::-webkit-scrollbar,
.content-display::-webkit-scrollbar {
  width: 6px;
}

.clipboard-list::-webkit-scrollbar-track,
.content-display::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.clipboard-list::-webkit-scrollbar-thumb,
.content-display::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.clipboard-list::-webkit-scrollbar-thumb:hover,
.content-display::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
