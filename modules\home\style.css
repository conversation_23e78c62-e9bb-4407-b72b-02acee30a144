/* Türkçe: <PERSON> say<PERSON> mod<PERSON> stilleri */

.home-container {
  padding: 40px;
  text-align: center;
  height: 100%;
  overflow-y: auto;
}

.welcome-header {
  margin-bottom: 40px;
}

.welcome-icon {
  font-size: 64px;
  color: var(--accent-color);
  margin-bottom: 24px;
  display: block;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.welcome-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 32px auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto 40px auto;
}

.feature-card {
  background: var(--bg-secondary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px var(--shadow);
  border-color: var(--accent-color);
}

.feature-card i {
  font-size: 32px;
  color: var(--accent-color);
  margin-bottom: 16px;
  display: block;
}

.feature-card h3 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 1.25rem;
}

.feature-card p {
  color: var(--text-muted);
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.5;
}

.feature-card code {
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--accent-color);
  font-weight: 600;
}

.tips-section {
  margin-top: 40px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-align: left;
}

.tips-section h3 {
  color: var(--text-primary);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tips-section h3 i {
  color: var(--warning-color);
}

.tips-section p {
  color: var(--text-secondary);
  margin-bottom: 8px;
  line-height: 1.5;
}

.tips-section code {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: var(--accent-color);
}
