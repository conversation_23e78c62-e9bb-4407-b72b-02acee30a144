// Türkçe: <PERSON>yarlar modülü - uygulama konfigürasyonu

;((context) => {
  const settingsModule = {
    settings: {},
    defaultSettings: {
      theme: 'dark',
      language: 'tr',
      autoSave: true,
      notifications: true,
      soundEffects: false,
      fontSize: 'medium',
      animationSpeed: 'normal',
      startupModule: 'home',
      geminiApiKey: '',
      maxHistoryItems: 100,
      autoUpdate: true,
      debugMode: false,
      compactMode: false,
      showWelcome: true
    },

    init() {
      this.loadSettings()
      this.renderSettingsInterface()
      this.setupEventListeners()
      context.updateStatus("Ayarlar modülü hazır")
    },

    loadSettings() {
      const saved = localStorage.getItem('app_settings')
      if (saved) {
        try {
          this.settings = { ...this.defaultSettings, ...JSON.parse(saved) }
        } catch (error) {
          console.error('Ayarlar yüklenemedi:', error)
          this.settings = { ...this.defaultSettings }
        }
      } else {
        this.settings = { ...this.defaultSettings }
      }
    },

    saveSettings() {
      localStorage.setItem('app_settings', JSON.stringify(this.settings))
      this.applySettings()
      context.updateStatus('Ayarlar kaydedildi')
    },

    applySettings() {
      // Türkçe: Tema uygula
      document.body.className = this.settings.theme === 'dark' ? 'theme-dark' : 'theme-light'
      
      // Türkçe: Font boyutu uygula
      const fontSizes = {
        'small': '14px',
        'medium': '16px',
        'large': '18px'
      }
      document.documentElement.style.fontSize = fontSizes[this.settings.fontSize] || '16px'
      
      // Türkçe: Kompakt mod
      document.body.classList.toggle('compact-mode', this.settings.compactMode)
      
      // Türkçe: Animasyon hızı
      const animationSpeeds = {
        'slow': '0.5s',
        'normal': '0.3s',
        'fast': '0.1s'
      }
      document.documentElement.style.setProperty('--animation-speed', animationSpeeds[this.settings.animationSpeed] || '0.3s')
    },

    renderSettingsInterface() {
      context.content.innerHTML = `
        <div class="settings-container">
          <div class="settings-header">
            <h2><i class="fas fa-cog"></i> Ayarlar</h2>
            <div class="settings-actions">
              <button class="settings-btn secondary" id="reset-btn">
                <i class="fas fa-undo"></i> Sıfırla
              </button>
              <button class="settings-btn primary" id="save-btn">
                <i class="fas fa-save"></i> Kaydet
              </button>
            </div>
          </div>
          
          <div class="settings-content">
            <div class="settings-tabs">
              <button class="tab-btn active" data-tab="general">
                <i class="fas fa-sliders-h"></i> Genel
              </button>
              <button class="tab-btn" data-tab="appearance">
                <i class="fas fa-palette"></i> Görünüm
              </button>
              <button class="tab-btn" data-tab="modules">
                <i class="fas fa-th-large"></i> Modüller
              </button>
              <button class="tab-btn" data-tab="api">
                <i class="fas fa-key"></i> API Anahtarları
              </button>
              <button class="tab-btn" data-tab="advanced">
                <i class="fas fa-cogs"></i> Gelişmiş
              </button>
            </div>
            
            <div class="settings-panels">
              ${this.renderGeneralPanel()}
              ${this.renderAppearancePanel()}
              ${this.renderModulesPanel()}
              ${this.renderApiPanel()}
              ${this.renderAdvancedPanel()}
            </div>
          </div>
        </div>
      `
      
      this.updateFormValues()
    },

    renderGeneralPanel() {
      return `
        <div class="settings-panel active" data-panel="general">
          <h3>Genel Ayarlar</h3>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Dil</span>
              <select class="setting-input" data-setting="language">
                <option value="tr">Türkçe</option>
                <option value="en">English</option>
              </select>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Başlangıç Modülü</span>
              <select class="setting-input" data-setting="startupModule">
                <option value="home">Ana Sayfa</option>
                <option value="file-search">Dosya Arama</option>
                <option value="ai">AI Asistan</option>
                <option value="clipboard">Pano Geçmişi</option>
                <option value="web-search">Web Arama</option>
                <option value="calculator">Hesap Makinesi</option>
              </select>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Maksimum Geçmiş Öğesi</span>
              <input type="number" class="setting-input" data-setting="maxHistoryItems" min="10" max="1000" step="10">
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="autoSave">
              <span class="checkbox-custom"></span>
              <span>Otomatik Kaydetme</span>
            </label>
            <small>Değişiklikler otomatik olarak kaydedilir</small>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="notifications">
              <span class="checkbox-custom"></span>
              <span>Bildirimler</span>
            </label>
            <small>Sistem bildirimleri göster</small>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="soundEffects">
              <span class="checkbox-custom"></span>
              <span>Ses Efektleri</span>
            </label>
            <small>Buton tıklama ve bildirim sesleri</small>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="showWelcome">
              <span class="checkbox-custom"></span>
              <span>Hoş Geldin Ekranı</span>
            </label>
            <small>Uygulama açılışında hoş geldin ekranını göster</small>
          </div>
        </div>
      `
    },

    renderAppearancePanel() {
      return `
        <div class="settings-panel" data-panel="appearance">
          <h3>Görünüm Ayarları</h3>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Tema</span>
              <div class="theme-selector">
                <label class="theme-option">
                  <input type="radio" name="theme" value="light" data-setting="theme">
                  <div class="theme-preview light">
                    <div class="theme-header"></div>
                    <div class="theme-content"></div>
                  </div>
                  <span>Açık Tema</span>
                </label>
                <label class="theme-option">
                  <input type="radio" name="theme" value="dark" data-setting="theme">
                  <div class="theme-preview dark">
                    <div class="theme-header"></div>
                    <div class="theme-content"></div>
                  </div>
                  <span>Koyu Tema</span>
                </label>
              </div>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Font Boyutu</span>
              <select class="setting-input" data-setting="fontSize">
                <option value="small">Küçük</option>
                <option value="medium">Orta</option>
                <option value="large">Büyük</option>
              </select>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Animasyon Hızı</span>
              <select class="setting-input" data-setting="animationSpeed">
                <option value="slow">Yavaş</option>
                <option value="normal">Normal</option>
                <option value="fast">Hızlı</option>
              </select>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="compactMode">
              <span class="checkbox-custom"></span>
              <span>Kompakt Mod</span>
            </label>
            <small>Daha az boşluk kullanarak daha fazla içerik göster</small>
          </div>
        </div>
      `
    },

    renderModulesPanel() {
      return `
        <div class="settings-panel" data-panel="modules">
          <h3>Modül Ayarları</h3>
          
          <div class="modules-list">
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-home"></i>
                <div>
                  <h4>Ana Sayfa</h4>
                  <p>Hoş geldin ekranı ve genel bilgiler</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked disabled>
                <span class="toggle-slider"></span>
              </label>
            </div>
            
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-search"></i>
                <div>
                  <h4>Dosya Arama</h4>
                  <p>Dosya ve klasörleri arar</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-brain"></i>
                <div>
                  <h4>AI Asistan</h4>
                  <p>Gemini AI ile sohbet</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-clipboard"></i>
                <div>
                  <h4>Pano Geçmişi</h4>
                  <p>Kopyalanan metinleri yönetir</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-globe"></i>
                <div>
                  <h4>Web Arama</h4>
                  <p>İnternette arama yapar</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
            
            <div class="module-setting">
              <div class="module-info">
                <i class="fas fa-calculator"></i>
                <div>
                  <h4>Hesap Makinesi</h4>
                  <p>Matematiksel işlemler</p>
                </div>
              </div>
              <label class="setting-toggle">
                <input type="checkbox" checked>
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
      `
    },

    renderApiPanel() {
      return `
        <div class="settings-panel" data-panel="api">
          <h3>API Anahtarları</h3>
          
          <div class="setting-group">
            <label class="setting-label">
              <span>Gemini AI API Anahtarı</span>
              <div class="api-key-input">
                <input type="password" class="setting-input" data-setting="geminiApiKey" placeholder="API anahtarınızı girin...">
                <button class="toggle-visibility-btn" type="button">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </label>
            <small>
              <i class="fas fa-info-circle"></i>
              API anahtarını <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>'dan alabilirsiniz
            </small>
          </div>
          
          <div class="api-status">
            <h4>API Durumu</h4>
            <div class="status-item">
              <span class="status-label">Gemini AI:</span>
              <span class="status-indicator" id="gemini-status">
                <i class="fas fa-circle"></i> Kontrol ediliyor...
              </span>
            </div>
          </div>
          
          <div class="api-usage">
            <h4>Kullanım İstatistikleri</h4>
            <div class="usage-stats">
              <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">Bu Ay AI Sorgusu</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">0</div>
                <div class="stat-label">Toplam Sorgu</div>
              </div>
            </div>
          </div>
        </div>
      `
    },

    renderAdvancedPanel() {
      return `
        <div class="settings-panel" data-panel="advanced">
          <h3>Gelişmiş Ayarlar</h3>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="autoUpdate">
              <span class="checkbox-custom"></span>
              <span>Otomatik Güncelleme</span>
            </label>
            <small>Uygulama güncellemelerini otomatik olarak kontrol et</small>
          </div>
          
          <div class="setting-group">
            <label class="setting-checkbox">
              <input type="checkbox" data-setting="debugMode">
              <span class="checkbox-custom"></span>
              <span>Debug Modu</span>
            </label>
            <small>Geliştirici konsolu ve hata ayıklama bilgileri</small>
          </div>
          
          <div class="setting-group">
            <h4>Veri Yönetimi</h4>
            <div class="data-actions">
              <button class="data-btn" id="clear-cache-btn">
                <i class="fas fa-broom"></i>
                Önbelleği Temizle
              </button>
              <button class="data-btn" id="clear-history-btn">
                <i class="fas fa-history"></i>
                Geçmişi Temizle
              </button>
              <button class="data-btn danger" id="reset-app-btn">
                <i class="fas fa-exclamation-triangle"></i>
                Uygulamayı Sıfırla
              </button>
            </div>
          </div>
          
          <div class="setting-group">
            <h4>Yedekleme</h4>
            <div class="backup-actions">
              <button class="backup-btn" id="export-data-btn">
                <i class="fas fa-download"></i>
                Verileri Dışa Aktar
              </button>
              <button class="backup-btn" id="import-data-btn">
                <i class="fas fa-upload"></i>
                Verileri İçe Aktar
              </button>
            </div>
          </div>
          
          <div class="setting-group">
            <h4>Uygulama Bilgileri</h4>
            <div class="app-info">
              <div class="info-item">
                <span class="info-label">Sürüm:</span>
                <span class="info-value">1.0.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">Electron:</span>
                <span class="info-value">27.0.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">Node.js:</span>
                <span class="info-value">18.17.1</span>
              </div>
            </div>
          </div>
        </div>
      `
    },

    setupEventListeners() {
      // Türkçe: Tab değiştirme
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('tab-btn')) {
          this.switchTab(e.target.dataset.tab)
        }
      })

      // Türkçe: Ayar değişiklikleri
      document.addEventListener('change', (e) => {
        if (e.target.dataset.setting) {
          this.updateSetting(e.target.dataset.setting, e.target)
        }
      })

      // Türkçe: Kaydet butonu
      document.getElementById('save-btn').addEventListener('click', () => {
        this.saveSettings()
      })

      // Türkçe: Sıfırla butonu
      document.getElementById('reset-btn').addEventListener('click', () => {
        this.resetSettings()
      })

      // Türkçe: API anahtarı görünürlük
      document.addEventListener('click', (e) => {
        if (e.target.closest('.toggle-visibility-btn')) {
          this.togglePasswordVisibility(e.target.closest('.toggle-visibility-btn'))
        }
      })

      // Türkçe: Veri yönetimi butonları
      this.setupDataManagementListeners()
    },

    setupDataManagementListeners() {
      const clearCacheBtn = document.getElementById('clear-cache-btn')
      const clearHistoryBtn = document.getElementById('clear-history-btn')
      const resetAppBtn = document.getElementById('reset-app-btn')
      const exportDataBtn = document.getElementById('export-data-btn')
      const importDataBtn = document.getElementById('import-data-btn')

      clearCacheBtn?.addEventListener('click', () => {
        this.clearCache()
      })

      clearHistoryBtn?.addEventListener('click', () => {
        this.clearHistory()
      })

      resetAppBtn?.addEventListener('click', () => {
        this.resetApp()
      })

      exportDataBtn?.addEventListener('click', () => {
        this.exportData()
      })

      importDataBtn?.addEventListener('click', () => {
        this.importData()
      })
    },

    switchTab(tabName) {
      // Türkçe: Tab butonlarını güncelle
      document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName)
      })

      // Türkçe: Panel'leri güncelle
      document.querySelectorAll('.settings-panel').forEach(panel => {
        panel.classList.toggle('active', panel.dataset.panel === tabName)
      })
    },

    updateSetting(settingName, element) {
      let value

      if (element.type === 'checkbox') {
        value = element.checked
      } else if (element.type === 'radio') {
        if (element.checked) {
          value = element.value
        } else {
          return // Radio button değişikliği sadece seçili olduğunda işlenir
        }
      } else {
        value = element.value
      }

      this.settings[settingName] = value

      // Türkçe: Otomatik kaydetme aktifse kaydet
      if (this.settings.autoSave) {
        this.saveSettings()
      }

      context.updateStatus(`${settingName} güncellendi`)
    },

    updateFormValues() {
      // Türkçe: Form elemanlarını mevcut ayarlarla doldur
      Object.keys(this.settings).forEach(key => {
        const elements = document.querySelectorAll(`[data-setting="${key}"]`)
        
        elements.forEach(element => {
          if (element.type === 'checkbox') {
            element.checked = this.settings[key]
          } else if (element.type === 'radio') {
            element.checked = element.value === this.settings[key]
          } else {
            element.value = this.settings[key]
          }
        })
      })
    },

    resetSettings() {
      if (confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
        this.settings = { ...this.defaultSettings }
        this.updateFormValues()
        this.saveSettings()
        context.updateStatus('Ayarlar sıfırlandı')
      }
    },

    togglePasswordVisibility(button) {
      const input = button.parentElement.querySelector('input')
      const icon = button.querySelector('i')

      if (input.type === 'password') {
        input.type = 'text'
        icon.className = 'fas fa-eye-slash'
      } else {
        input.type = 'password'
        icon.className = 'fas fa-eye'
      }
    },

    clearCache() {
      if (confirm('Önbelleği temizlemek istediğinizden emin misiniz?')) {
        // Türkçe: Önbellek temizleme işlemi
        localStorage.removeItem('ai_chat_history')
        localStorage.removeItem('web_search_history')
        localStorage.removeItem('calculator_history')
        
        context.updateStatus('Önbellek temizlendi')
      }
    },

    clearHistory() {
      if (confirm('Tüm geçmişi temizlemek istediğinizden emin misiniz?')) {
        // Türkçe: Geçmiş temizleme
        localStorage.removeItem('ai_chat_history')
        localStorage.removeItem('web_search_history')
        localStorage.removeItem('calculator_history')
        localStorage.removeItem('web_bookmarks')
        
        context.updateStatus('Geçmiş temizlendi')
      }
    },

    resetApp() {
      if (confirm('Uygulamayı tamamen sıfırlamak istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
        if (confirm('Bu işlem tüm verilerinizi silecek. Devam etmek istediğinizden emin misiniz?')) {
          // Türkçe: Tüm localStorage'ı temizle
          localStorage.clear()
          
          // Türkçe: Sayfayı yenile
          location.reload()
        }
      }
    },

    exportData() {
      try {
        const exportData = {
          settings: this.settings,
          aiChatHistory: JSON.parse(localStorage.getItem('ai_chat_history') || '[]'),
          webSearchHistory: JSON.parse(localStorage.getItem('web_search_history') || '[]'),
          calculatorHistory: JSON.parse(localStorage.getItem('calculator_history') || '[]'),
          webBookmarks: JSON.parse(localStorage.getItem('web_bookmarks') || '[]'),
          exportDate: new Date().toISOString(),
          version: '1.0.0'
        }

        const dataStr = JSON.stringify(exportData, null, 2)
        const blob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        
        const a = document.createElement('a')
        a.href = url
        a.download = `modular-app-backup-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        
        URL.revokeObjectURL(url)
        context.updateStatus('Veriler dışa aktarıldı')
      } catch (error) {
        context.updateStatus('Dışa aktarma hatası')
      }
    },

    importData() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (!file) return

        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const importData = JSON.parse(e.target.result)
            
            if (confirm('İçe aktarma işlemi mevcut verilerinizi değiştirecek. Devam etmek istediğinizden emin misiniz?')) {
              // Türkçe: Verileri içe aktar
              if (importData.settings) {
                this.settings = { ...this.defaultSettings, ...importData.settings }
                localStorage.setItem('app_settings', JSON.stringify(this.settings))
              }
              
              if (importData.aiChatHistory) {
                localStorage.setItem('ai_chat_history', JSON.stringify(importData.aiChatHistory))
              }
              
              if (importData.webSearchHistory) {
                localStorage.setItem('web_search_history', JSON.stringify(importData.webSearchHistory))
              }
              
              if (importData.calculatorHistory) {
                localStorage.setItem('calculator_history', JSON.stringify(importData.calculatorHistory))
              }
              
              if (importData.webBookmarks) {
                localStorage.setItem('web_bookmarks', JSON.stringify(importData.webBookmarks))
              }
              
              this.updateFormValues()
              this.applySettings()
              context.updateStatus('Veriler içe aktarıldı')
            }
          } catch (error) {
            context.updateStatus('İçe aktarma hatası: Geçersiz dosya')
          }
        }
        
        reader.readAsText(file)
      }
      
      input.click()
    },

    executeAction(actionId) {
      switch (actionId) {
        case 'save_settings':
          this.saveSettings()
          break
        case 'reset_settings':
          this.resetSettings()
          break
        case 'export_settings':
          this.exportData()
          break
        case 'import_settings':
          this.importData()
          break
      }
    }
  }

  // Türkçe: Global erişim için modül instance'ını kaydet
  window.currentModuleInstance = settingsModule

  // Türkçe: Modülü başlat
  settingsModule.init()
})(window.context)
