/* Türkçe: AI Asistan modülü stilleri */

.ai-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chat-title i {
  font-size: 1.5rem;
  color: var(--accent-color);
}

.chat-title h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mode-selector {
  display: flex;
  gap: 0.5rem;
  background: var(--bg-primary);
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid var(--border-color);
}

.mode-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.mode-option:hover {
  background: var(--bg-secondary);
}

.mode-option input[type="radio"] {
  display: none;
}

.mode-option input[type="radio"]:checked + span {
  background: var(--accent-color);
  color: white;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  margin: -0.5rem -0.75rem;
}

.mode-option span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.control-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-message {
  display: flex;
  gap: 1rem;
  padding: 2rem;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.ai-avatar {
  width: 60px;
  height: 60px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.welcome-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.welcome-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.example-prompts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.prompt-item {
  padding: 0.75rem 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.prompt-item:hover {
  border-color: var(--accent-color);
  background: var(--accent-color);
  color: white;
  transform: translateY(-1px);
}

.message {
  display: flex;
  gap: 0.75rem;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.1rem;
}

.user-message .message-avatar {
  background: var(--accent-color);
  color: white;
}

.assistant-message .message-avatar {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.message-bubble {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  position: relative;
}

.user-message .message-bubble {
  background: var(--accent-color);
  color: white;
}

.user-message .message-bubble .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-content {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-content strong {
  font-weight: 600;
}

.message-content em {
  font-style: italic;
}

.message-content code {
  background: var(--bg-tertiary);
  color: var(--accent-color);
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
}

.user-message .message-content code {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.message-content pre {
  background: var(--bg-tertiary);
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.message-content pre code {
  background: none;
  color: var(--text-primary);
  padding: 0;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.5rem;
}

.loading-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: var(--text-muted);
  border-radius: 50%;
  animation: loading-pulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-container {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.image-upload-area {
  margin-bottom: 1rem;
}

.upload-zone {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-primary);
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: var(--accent-color);
  background: var(--bg-secondary);
}

.upload-zone i {
  font-size: 2rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.upload-zone p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.uploaded-images {
  margin-top: 1rem;
}

.uploaded-image-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.uploaded-image-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.image-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-name {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.remove-image-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: var(--text-muted);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: #e74c3c;
}

.input-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

#chat-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.4;
  resize: none;
  outline: none;
  transition: border-color 0.2s ease;
}

#chat-input:focus {
  border-color: var(--accent-color);
}

#chat-input::placeholder {
  color: var(--text-muted);
}

.send-btn {
  width: 40px;
  height: 40px;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: scale(1.05);
}

.send-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.model-info,
.mode-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

#current-model,
#current-mode {
  color: var(--accent-color);
  font-weight: 500;
}

/* Türkçe: Scrollbar stilleri */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Türkçe: Üretilen resim stilleri */
.generated-image-container {
  margin: 0.5rem 0;
  text-align: center;
}

.generated-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-caption {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-style: italic;
}
