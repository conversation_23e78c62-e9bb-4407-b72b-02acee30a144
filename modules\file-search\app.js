// Türkçe: Dosya arama modülü - gerçek dosya arama

const context = require("./context") // Declare the context variable

module.exports = {
  searchResults: [],
  selectedFile: null,
  currentQuery: "",
  searchTimeout: null,

  run() {
    this.renderSearchInterface()
    this.setupEventListeners()
    context.updateStatus("Dosya arama modülü hazır")
  },

  stop() {
    // Türkçe: Temizlik işlemleri
    clearTimeout(this.searchTimeout)
  },

  handleQuery(query) {
    if (query.trim()) {
      this.performSearch(query.trim())
    }
  },

  renderSearchInterface() {
    // Türkçe: Sol panel - arama sonuçları listesi
    context.sidebar.innerHTML = `
      <div class="search-panel">
        <div class="search-header">
          <h3><i class="fas fa-search"></i> Dosya Arama</h3>
          <div class="search-input-container">
            <input type="text" id="file-search-input" placeholder="Dosya veya klasör adı..." class="search-input-field">
            <button id="search-btn" class="search-btn">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="search-results" id="search-results">
          <div class="no-results">
            <i class="fas fa-folder-open"></i>
            <p>Arama yapmak için yukarıdaki alana dosya adı yazın</p>
          </div>
        </div>
      </div>
    `

    // Türkçe: Sağ panel - dosya önizleme
    context.content.innerHTML = `
      <div class="file-preview">
        <div class="preview-placeholder">
          <i class="fas fa-file-alt"></i>
          <h3>Dosya Önizleme</h3>
          <p>Bir dosya seçin ve burada önizlemesini görün</p>
        </div>
      </div>
    `
  },

  setupEventListeners() {
    const searchInput = document.getElementById("file-search-input")
    const searchBtn = document.getElementById("search-btn")

    // Türkçe: Arama input olayları
    searchInput.addEventListener("input", (e) => {
      if (e.target.value.length > 2) {
        this.debounceSearch(e.target.value)
      }
    })

    searchInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        this.performSearch(e.target.value)
      }
    })

    searchBtn.addEventListener("click", () => {
      this.performSearch(searchInput.value)
    })
  },

  debounceSearch(query) {
    clearTimeout(this.searchTimeout)
    this.searchTimeout = setTimeout(() => {
      this.performSearch(query)
    }, 500)
  },

  async performSearch(query) {
    if (!query || query.length < 2) return

    this.currentQuery = query
    context.updateStatus(`"${query}" aranıyor...`)

    try {
      // Türkçe: Aktif filtreleri al
      const filters = context.getActiveFilters()

      // Türkçe: Dosya arama API'sini çağır
      const results = await context.electronAPI.searchFiles(query, filters)

      this.searchResults = results
      this.renderSearchResults()

      context.updateStatus(`${results.length} sonuç bulundu`)
    } catch (error) {
      console.error("Arama hatası:", error)
      context.updateStatus("Arama sırasında hata oluştu")
    }
  },

  renderSearchResults() {
    const resultsContainer = document.getElementById("search-results")

    if (this.searchResults.length === 0) {
      resultsContainer.innerHTML = `
        <div class="no-results">
          <i class="fas fa-search"></i>
          <p>"${this.currentQuery}" için sonuç bulunamadı</p>
        </div>
      `
      return
    }

    const resultsHTML = this.searchResults
      .map(
        (file, index) => `
        <div class="file-item" data-index="${index}" data-path="${file.path}">
          <div class="file-icon">
            ${this.getFileIcon(file)}
          </div>
          <div class="file-info">
            <div class="file-name">${file.name}</div>
            <div class="file-path">${file.path}</div>
            <div class="file-meta">
              ${file.type} ${file.size ? `• ${this.formatFileSize(file.size)}` : ""}
            </div>
          </div>
        </div>
      `,
      )
      .join("")

    resultsContainer.innerHTML = `
      <div class="results-header">
        <span class="results-count">${this.searchResults.length} sonuç</span>
      </div>
      <div class="results-list">
        ${resultsHTML}
      </div>
    `

    // Türkçe: Dosya öğelerine tıklama olayı ekle
    const fileItems = resultsContainer.querySelectorAll(".file-item")
    fileItems.forEach((item) => {
      item.addEventListener("click", () => {
        this.selectFile(Number.parseInt(item.dataset.index))
      })

      item.addEventListener("dblclick", () => {
        this.executeAction("open")
      })
    })
  },

  getFileIcon(file) {
    const ext = file.type.toLowerCase()

    if (file.type === "folder") return '<i class="fas fa-folder text-blue-500"></i>'
    if ([".jpg", ".jpeg", ".png", ".gif", ".bmp"].includes(ext)) return '<i class="fas fa-image text-green-500"></i>'
    if ([".mp4", ".avi", ".mkv", ".mov"].includes(ext)) return '<i class="fas fa-video text-red-500"></i>'
    if ([".mp3", ".wav", ".flac", ".aac"].includes(ext)) return '<i class="fas fa-music text-purple-500"></i>'
    if ([".pdf"].includes(ext)) return '<i class="fas fa-file-pdf text-red-600"></i>'
    if ([".doc", ".docx", ".txt", ".rtf"].includes(ext)) return '<i class="fas fa-file-word text-blue-600"></i>'
    if ([".xls", ".xlsx"].includes(ext)) return '<i class="fas fa-file-excel text-green-600"></i>'
    if ([".zip", ".rar", ".7z"].includes(ext)) return '<i class="fas fa-file-archive text-orange-500"></i>'

    return '<i class="fas fa-file text-gray-500"></i>'
  },

  selectFile(index) {
    // Türkçe: Önceki seçimi temizle
    const prevSelected = document.querySelector(".file-item.selected")
    if (prevSelected) {
      prevSelected.classList.remove("selected")
    }

    // Türkçe: Yeni seçimi işaretle
    const fileItems = document.querySelectorAll(".file-item")
    if (fileItems[index]) {
      fileItems[index].classList.add("selected")
      this.selectedFile = this.searchResults[index]

      // Türkçe: Dosya önizlemesini göster
      this.showFilePreview(this.selectedFile)

      // Türkçe: Seçim bilgisini güncelle
      context.updateSelection(`${this.selectedFile.name} seçildi`)
    }
  },

  showFilePreview(file) {
    const previewContainer = context.content.querySelector(".file-preview")

    const previewHTML = `
      <div class="preview-header">
        <div class="file-icon-large">
          ${this.getFileIcon(file)}
        </div>
        <div class="file-details">
          <h2 class="file-title">${file.name}</h2>
          <div class="file-properties">
            <div class="property">
              <span class="property-label">Konum:</span>
              <span class="property-value">${file.path}</span>
            </div>
            <div class="property">
              <span class="property-label">Tür:</span>
              <span class="property-value">${file.type || "Bilinmiyor"}</span>
            </div>
            ${
              file.size
                ? `
                <div class="property">
                  <span class="property-label">Boyut:</span>
                  <span class="property-value">${this.formatFileSize(file.size)}</span>
                </div>
              `
                : ""
            }
          </div>
        </div>
      </div>
      <div class="preview-content">
        ${this.generatePreviewContent(file)}
      </div>
      <div class="preview-actions">
        <button class="preview-action-btn primary" onclick="window.currentModuleInstance.executeAction('open')">
          <i class="fas fa-folder-open"></i> Aç
        </button>
        <button class="preview-action-btn" onclick="window.currentModuleInstance.executeAction('show_in_folder')">
          <i class="fas fa-external-link-alt"></i> Klasörde Göster
        </button>
        <button class="preview-action-btn" onclick="window.currentModuleInstance.executeAction('copy_path')">
          <i class="fas fa-copy"></i> Yolu Kopyala
        </button>
      </div>
    `

    previewContainer.innerHTML = previewHTML
  },

  generatePreviewContent(file) {
    const ext = file.type.toLowerCase()

    // Türkçe: Resim önizlemesi
    if ([".jpg", ".jpeg", ".png", ".gif", ".bmp"].includes(ext)) {
      return `
        <div class="image-preview">
          <img src="file://${file.path}" alt="${file.name}" 
               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
          <div class="preview-error" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Resim önizlenemiyor</p>
          </div>
        </div>
      `
    }

    // Türkçe: Varsayılan önizleme
    return `
      <div class="default-preview">
        <i class="fas fa-eye-slash"></i>
        <p>Bu dosya türü için önizleme mevcut değil</p>
        <p>Dosyayı açmak için "Aç" butonuna tıklayın</p>
      </div>
    `
  },

  executeAction(actionId) {
    if (!this.selectedFile) {
      context.updateStatus("Önce bir dosya seçin")
      return
    }

    switch (actionId) {
      case "open":
        this.openFile()
        break
      case "show_in_folder":
        this.showInFolder()
        break
      case "copy_path":
        this.copyPath()
        break
    }
  },

  async openFile() {
    try {
      const result = await context.electronAPI.openFile(this.selectedFile.path)
      if (result.success) {
        context.updateStatus(`${this.selectedFile.name} açıldı`)
      } else {
        context.updateStatus("Dosya açılamadı: " + result.error)
      }
    } catch (error) {
      context.updateStatus("Dosya açılırken hata oluştu")
    }
  },

  async showInFolder() {
    try {
      const result = await context.electronAPI.showInFolder(this.selectedFile.path)
      if (result.success) {
        context.updateStatus("Klasörde gösterildi")
      } else {
        context.updateStatus("Klasör açılamadı: " + result.error)
      }
    } catch (error) {
      context.updateStatus("Klasör açılırken hata oluştu")
    }
  },

  async copyPath() {
    try {
      await context.electronAPI.copyToClipboard(this.selectedFile.path)
      context.updateStatus("Dosya yolu kopyalandı")
    } catch (error) {
      context.updateStatus("Yol kopyalanamadı")
    }
  },

  formatFileSize(bytes) {
    if (!bytes) return "0 B"
    const sizes = ["B", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i]
  },
}
