/* Türkçe: Web arama modülü stilleri */

.web-search-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.search-header h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-input-container {
  display: flex;
  gap: 0.5rem;
}

.search-input-field {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.search-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
}

.search-btn {
  padding: 0.75rem 1rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background: var(--accent-hover);
}

.search-history {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.search-history h4 {
  margin: 0 0 0.75rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.no-history {
  color: var(--text-muted);
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem 0;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: var(--bg-tertiary);
}

.history-content {
  flex: 1;
  min-width: 0;
}

.history-query {
  display: block;
  font-size: 0.875rem;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-time {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.125rem;
}

.delete-history-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s;
}

.history-item:hover .delete-history-btn {
  opacity: 1;
}

.delete-history-btn:hover {
  background: var(--danger-color);
  color: white;
}

.search-results {
  flex: 1;
  overflow-y: auto;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.no-results i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.results-count {
  color: var(--text-primary);
  font-weight: 500;
}

.search-engine {
  color: var(--text-muted);
}

.results-list {
  padding: 0.5rem;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
}

.result-item:hover {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

.result-item.selected {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.result-favicon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.result-favicon img {
  width: 100%;
  height: 100%;
  border-radius: 3px;
}

.result-favicon i {
  font-size: 16px;
  color: var(--text-muted);
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  line-height: 1.3;
  color: var(--text-primary);
}

.result-item.selected .result-title {
  color: white;
}

.result-url {
  font-size: 0.875rem;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
  word-break: break-all;
}

.result-item.selected .result-url {
  color: rgba(255, 255, 255, 0.9);
}

.result-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.result-item.selected .result-description {
  color: rgba(255, 255, 255, 0.8);
}

.result-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.result-item.selected .result-meta {
  color: rgba(255, 255, 255, 0.7);
}

.result-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.result-item:hover .result-actions,
.result-item.selected .result-actions {
  opacity: 1;
}

.result-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.result-action-btn:hover {
  background: var(--accent-color);
  color: white;
}

.result-item.selected .result-action-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.result-item.selected .result-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Türkçe: Önizleme stilleri */
.result-preview {
  height: 100%;
  overflow-y: auto;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
  padding: 2rem;
}

.preview-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.search-engines {
  margin-top: 2rem;
}

.search-engines h4 {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.engine-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.engine-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.engine-item i {
  font-size: 1.25rem;
  color: var(--accent-color);
}

.preview-header {
  display: flex;
  align-items: flex-start;
  padding: 2rem;
  border-bottom: 1px solid var(--border-color);
  gap: 1rem;
}

.preview-favicon {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.preview-favicon img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.preview-favicon i {
  font-size: 24px;
  color: var(--text-muted);
}

.preview-info {
  flex: 1;
}

.preview-title {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  line-height: 1.3;
}

.preview-url {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.875rem;
  word-break: break-all;
  display: block;
  margin-bottom: 1rem;
}

.preview-url:hover {
  text-decoration: underline;
}

.preview-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  flex-wrap: wrap;
}

.preview-meta span {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.preview-content {
  padding: 2rem;
}

.preview-description {
  margin-bottom: 2rem;
}

.preview-description h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.preview-description p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.preview-iframe-container h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.iframe-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-muted);
  text-align: center;
}

.iframe-placeholder i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.preview-open-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;
}

.preview-open-btn:hover {
  background: var(--accent-hover);
}

.preview-actions {
  padding: 2rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.preview-action-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.preview-action-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.preview-action-btn.primary {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.preview-action-btn.primary:hover {
  background: var(--accent-hover);
}

/* Türkçe: Scrollbar stilleri */
.search-results::-webkit-scrollbar,
.result-preview::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track,
.result-preview::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.search-results::-webkit-scrollbar-thumb,
.result-preview::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover,
.result-preview::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
