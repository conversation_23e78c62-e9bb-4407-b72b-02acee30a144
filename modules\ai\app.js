// Türkçe: AI Asistan modülü - Gerçek Gemini API entegrasyonu

const context = {
  content: document.getElementById("content"),
  updateStatus: (status) => {
    console.log(status)
  },
  app: {
    geminiApiKey: "AIzaSyA9OZzaNhR-1VeaQgNgKOxftpfkha-sbHk",
    showApiModal: () => {
      alert("Lütfen API anahtarınızı girin.")
    },
  },
}

const aiModule = {
  chatHistory: [],
  isProcessing: false,
  currentMode: "chat",

  run() {
    this.loadChatHistory()
    this.renderChatInterface()
    this.setupEventListeners()
    context.updateStatus("AI Asistan hazır")
  },

  stop() {
    this.saveChatHistory()
  },

  handleQuery(query) {
    if (query.trim()) {
      this.sendMessage(query.trim())
    }
  },

  renderChatInterface() {
    context.content.innerHTML = `
      <div class="ai-chat-container">
        <div class="chat-header">
          <div class="chat-title">
            <i class="fas fa-brain"></i>
            <h2>AI Asistan</h2>
          </div>
          <div class="chat-controls">
            <button class="control-btn" id="clear-chat-btn" title="Sohbeti Temizle">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <div class="chat-messages" id="chat-messages">
          ${this.chatHistory.length === 0 ? this.getWelcomeMessage() : ""}
        </div>
        
        <div class="chat-input-container">
          <div class="input-wrapper">
            <textarea 
              id="chat-input" 
              placeholder="AI'ya bir soru sorun veya sohbet edin..."
              rows="1"
            ></textarea>
            <button id="send-btn" class="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
          <div class="input-footer">
            <span class="model-info">Model: <span id="current-model">gemini-2.0-flash</span></span>
            <span class="mode-info">Mod: <span id="current-mode">Chat</span></span>
          </div>
        </div>
      </div>
    `

    this.renderChatHistory()
  },

  getWelcomeMessage() {
    return `
      <div class="welcome-message">
        <div class="ai-avatar">
          <i class="fas fa-robot"></i>
        </div>
        <div class="welcome-content">
          <h3>AI Asistan'a Hoş Geldiniz!</h3>
          <p>Ben Gemini AI'yım. Size yardımcı olmak için buradayım. Sorularınızı sorun, kod yazımında yardım isteyin, yaratıcı projeler için fikir alın veya sadece sohbet edin!</p>
          <div class="example-prompts">
            <div class="prompt-item" data-prompt="JavaScript ile bir hesap makinesi nasıl yapılır?">
              💻 Kod yardımı
            </div>
            <div class="prompt-item" data-prompt="Bana kısa bir hikaye yaz">
              ✍️ Yaratıcı yazım
            </div>
            <div class="prompt-item" data-prompt="Python öğrenmek için en iyi kaynaklar neler?">
              📚 Öğrenme
            </div>
          </div>
        </div>
      </div>
    `
  },

  setupEventListeners() {
    const chatInput = document.getElementById("chat-input")
    const sendBtn = document.getElementById("send-btn")
    const clearBtn = document.getElementById("clear-chat-btn")

    // Türkçe: Metin alanı olayları
    chatInput.addEventListener("input", (e) => {
      this.handleInputChange(e.target.value)
    })

    chatInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        this.sendMessage()
      }
    })

    // Türkçe: Gönder butonu
    sendBtn.addEventListener("click", () => {
      this.sendMessage()
    })

    // Türkçe: Sohbeti temizle
    clearBtn.addEventListener("click", () => {
      this.clearChat()
    })

    // Türkçe: Örnek prompt'lara tıklama
    document.addEventListener("click", (e) => {
      if (e.target.classList.contains("prompt-item")) {
        const prompt = e.target.dataset.prompt
        chatInput.value = prompt
        this.handleInputChange(prompt)
        chatInput.focus()
      }
    })

    // Türkçe: Mod değişikliği dinle
    document.addEventListener("change", (e) => {
      if (e.target.name === "mode") {
        this.currentMode = e.target.value
        document.getElementById("current-mode").textContent = e.target.value === "chat" ? "Chat" : "Image"
      }
    })
  },

  handleInputChange(value) {
    const sendBtn = document.getElementById("send-btn")
    sendBtn.disabled = !value.trim() || this.isProcessing

    // Türkçe: Textarea yüksekliğini otomatik ayarla
    const textarea = document.getElementById("chat-input")
    textarea.style.height = "auto"
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px"
  },

  async sendMessage(message = null) {
    const chatInput = document.getElementById("chat-input")
    const messageText = message || chatInput.value.trim()

    if (!messageText || this.isProcessing) return

    const apiKey = context.app.geminiApiKey
    if (!apiKey) {
      context.app.showApiModal()
      return
    }

    // Türkçe: Kullanıcı mesajını ekle
    this.addMessage("user", messageText)
    chatInput.value = ""
    this.handleInputChange("")

    // Türkçe: AI yanıtını al
    await this.getAIResponse(messageText, apiKey)
  },

  async getAIResponse(message, apiKey) {
    this.isProcessing = true
    context.updateStatus("AI düşünüyor...")

    // Türkçe: Yükleniyor mesajı ekle
    const loadingId = this.addMessage("assistant", "", true)

    try {
      let response
      if (this.currentMode === "image") {
        response = await this.generateImage(message, apiKey)
      } else {
        response = await this.callGeminiAPI(message, apiKey)
      }

      // Türkçe: Yükleniyor mesajını güncelle
      this.updateMessage(loadingId, response)
      context.updateStatus("AI yanıtladı")
    } catch (error) {
      console.error("AI API hatası:", error)
      this.updateMessage(loadingId, "Üzgünüm, bir hata oluştu. Lütfen API anahtarınızı kontrol edin ve tekrar deneyin.")
      context.updateStatus("AI hatası")
    } finally {
      this.isProcessing = false
      this.handleInputChange(document.getElementById("chat-input").value)
    }
  },

  async callGeminiAPI(message, apiKey) {
    // Türkçe: Yeni Gemini API kullanımı
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: message,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2048,
          },
        }),
      },
    )

    if (!response.ok) {
      throw new Error(`API hatası: ${response.status}`)
    }

    const data = await response.json()

    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text
    } else {
      throw new Error("Geçersiz API yanıtı")
    }
  },

  async generateImage(prompt, apiKey) {
    // Türkçe: Resim oluşturma API'si
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `Create an image: ${prompt}`,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.8,
            maxOutputTokens: 1024,
          },
        }),
      },
    )

    if (!response.ok) {
      throw new Error(`Resim API hatası: ${response.status}`)
    }

    const data = await response.json()

    // Türkçe: Şimdilik metin yanıtı döndür, gerçek resim API'si farklı endpoint kullanır
    return `Resim oluşturma özelliği henüz aktif değil. Bunun yerine "${prompt}" hakkında açıklama yapabilirim.`
  },

  addMessage(role, content, isLoading = false) {
    const messageId = Date.now() + Math.random()
    const message = {
      id: messageId,
      role,
      content,
      timestamp: Date.now(),
      isLoading,
    }

    this.chatHistory.push(message)
    this.renderMessage(message)
    this.saveChatHistory()
    this.scrollToBottom()

    return messageId
  },

  updateMessage(messageId, content) {
    const message = this.chatHistory.find((m) => m.id === messageId)
    if (message) {
      message.content = content
      message.isLoading = false

      const messageElement = document.querySelector(`[data-message-id="${messageId}"]`)
      if (messageElement) {
        const contentElement = messageElement.querySelector(".message-content")
        contentElement.innerHTML = this.formatMessageContent(content)
      }

      this.saveChatHistory()
    }
  },

  renderMessage(message) {
    const messagesContainer = document.getElementById("chat-messages")

    // Türkçe: Hoş geldin mesajını kaldır
    const welcomeMessage = messagesContainer.querySelector(".welcome-message")
    if (welcomeMessage) {
      welcomeMessage.remove()
    }

    const messageElement = document.createElement("div")
    messageElement.className = `message ${message.role}-message`
    messageElement.dataset.messageId = message.id

    messageElement.innerHTML = `
      <div class="message-avatar">
        <i class="fas ${message.role === "user" ? "fa-user" : "fa-robot"}"></i>
      </div>
      <div class="message-bubble">
        <div class="message-content">
          ${message.isLoading ? this.getLoadingContent() : this.formatMessageContent(message.content)}
        </div>
        <div class="message-time">
          ${new Date(message.timestamp).toLocaleTimeString("tr-TR", { hour: "2-digit", minute: "2-digit" })}
        </div>
      </div>
    `

    messagesContainer.appendChild(messageElement)
  },

  getLoadingContent() {
    return `
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    `
  },

  formatMessageContent(content) {
    // Türkçe: Markdown benzeri formatlamalar
    return content
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/`(.*?)`/g, "<code>$1</code>")
      .replace(/```([\s\S]*?)```/g, "<pre><code>$1</code></pre>")
      .replace(/\n/g, "<br>")
  },

  renderChatHistory() {
    const messagesContainer = document.getElementById("chat-messages")
    messagesContainer.innerHTML = ""

    if (this.chatHistory.length === 0) {
      messagesContainer.innerHTML = this.getWelcomeMessage()
      return
    }

    this.chatHistory.forEach((message) => {
      this.renderMessage(message)
    })

    this.scrollToBottom()
  },

  scrollToBottom() {
    const messagesContainer = document.getElementById("chat-messages")
    messagesContainer.scrollTop = messagesContainer.scrollHeight
  },

  clearChat() {
    if (confirm("Sohbet geçmişini temizlemek istediğinizden emin misiniz?")) {
      this.chatHistory = []
      this.saveChatHistory()
      this.renderChatHistory()
      context.updateStatus("Sohbet temizlendi")
    }
  },

  saveChatHistory() {
    localStorage.setItem("ai_chat_history", JSON.stringify(this.chatHistory))
  },

  loadChatHistory() {
    const saved = localStorage.getItem("ai_chat_history")
    if (saved) {
      try {
        this.chatHistory = JSON.parse(saved)
      } catch (error) {
        console.error("Sohbet geçmişi yüklenemedi:", error)
        this.chatHistory = []
      }
    }
  },
}

export default aiModule
