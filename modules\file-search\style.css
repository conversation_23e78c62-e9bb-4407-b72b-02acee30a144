/* Türkçe: <PERSON><PERSON><PERSON> a<PERSON> modü<PERSON><PERSON> still<PERSON> */

.search-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.search-header h3 {
  margin-bottom: 1rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-input-container {
  display: flex;
  gap: 0.5rem;
}

.search-input-field {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.search-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
}

.search-btn {
  padding: 0.5rem 1rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background: var(--accent-hover);
}

.search-results {
  flex: 1;
  overflow-y: auto;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.no-results i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.results-header {
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.results-list {
  padding: 0.5rem;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.file-item:hover {
  background: var(--bg-tertiary);
}

.file-item.selected {
  background: var(--accent-color);
  color: white;
}

.file-item.selected .file-path,
.file-item.selected .file-meta {
  color: rgba(255, 255, 255, 0.8);
}

.file-icon {
  font-size: 1.25rem;
  width: 2rem;
  text-align: center;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-path {
  font-size: 0.75rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.125rem;
}

.file-meta {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Türkçe: Dosya önizleme stilleri */
.file-preview {
  height: 100%;
  overflow-y: auto;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
}

.preview-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.preview-header {
  display: flex;
  align-items: flex-start;
  padding: 2rem;
  border-bottom: 1px solid var(--border-color);
  gap: 1.5rem;
}

.file-icon-large {
  font-size: 3rem;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
}

.file-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  word-break: break-word;
}

.file-properties {
  display: grid;
  gap: 0.75rem;
}

.property {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.property-label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 4rem;
}

.property-value {
  color: var(--text-primary);
  word-break: break-all;
}

.preview-content {
  padding: 2rem;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow);
}

.text-preview {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 1rem;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}

.default-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.default-preview i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-muted);
  padding: 2rem;
}

.preview-error i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--warning-color);
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
}

.preview-loading i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--accent-color);
}

/* Türkçe: Renk yardımcıları */
.text-blue-500 {
  color: #3b82f6;
}
.text-green-500 {
  color: #10b981;
}
.text-red-500 {
  color: #ef4444;
}
.text-purple-500 {
  color: #8b5cf6;
}
.text-red-600 {
  color: #dc2626;
}
.text-blue-600 {
  color: #2563eb;
}
.text-green-600 {
  color: #059669;
}
.text-orange-500 {
  color: #f97316;
}
.text-gray-500 {
  color: #6b7280;
}
