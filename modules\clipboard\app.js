// Türkçe: Pano geçmişi modülü - kopyalanan metinleri yönetir

;((context) => {
  const clipboardModule = {
    clipboardHistory: [],
    selectedItem: null,
    filteredHistory: [],

    init() {
      this.renderClipboardInterface()
      this.setupEventListeners()
      this.loadClipboardHistory()
      context.updateStatus("Pano geçmişi yüklendi")
    },

    renderClipboardInterface() {
      // Türkçe: Sol panel - pano öğeleri listesi
      context.sidebar.innerHTML = `
        <div class="clipboard-panel">
          <div class="clipboard-header">
            <h3><i class="fas fa-clipboard"></i> Pano Geçmişi</h3>
            <div class="clipboard-controls">
              <button class="control-btn" id="refresh-btn" title="Yenile">
                <i class="fas fa-sync"></i>
              </button>
              <button class="control-btn" id="clear-all-btn" title="Tümünü Temizle">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          <div class="clipboard-stats">
            <span class="stat-item">
              <i class="fas fa-list"></i>
              <span id="total-count">0</span> öğe
            </span>
            <span class="stat-item">
              <i class="fas fa-clock"></i>
              <span id="last-update">-</span>
            </span>
          </div>
          <div class="clipboard-list" id="clipboard-list">
            <div class="no-items">
              <i class="fas fa-clipboard"></i>
              <p>Henüz pano geçmişi yok</p>
              <small>Bir şeyler kopyaladığınızda burada görünecek</small>
            </div>
          </div>
        </div>
      `

      // Türkçe: Sağ panel - seçili öğe detayları
      context.content.innerHTML = `
        <div class="clipboard-preview">
          <div class="preview-placeholder">
            <i class="fas fa-clipboard-list"></i>
            <h3>Pano Öğesi Önizleme</h3>
            <p>Bir pano öğesi seçin ve burada detaylarını görün</p>
          </div>
        </div>
      `
    },

    setupEventListeners() {
      const refreshBtn = document.getElementById('refresh-btn')
      const clearAllBtn = document.getElementById('clear-all-btn')

      refreshBtn.addEventListener('click', () => {
        this.loadClipboardHistory()
      })

      clearAllBtn.addEventListener('click', () => {
        this.clearAllHistory()
      })

      // Türkçe: Otomatik yenileme (5 saniyede bir)
      setInterval(() => {
        this.loadClipboardHistory()
      }, 5000)
    },

    async loadClipboardHistory() {
      try {
        const history = await context.electronAPI.getClipboardHistory()
        this.clipboardHistory = history
        this.applyFilters()
        this.renderClipboardList()
        this.updateStats()
      } catch (error) {
        console.error('Pano geçmişi yüklenemedi:', error)
        context.updateStatus('Pano geçmişi yüklenemedi')
      }
    },

    applyFilters() {
      let filtered = [...this.clipboardHistory]

      // Türkçe: İçerik tipi filtresi
      const contentTypeFilter = context.app.activeFilters.find(f => f.id === 'content_type')
      if (contentTypeFilter && contentTypeFilter.value !== 'Tümü') {
        filtered = filtered.filter(item => {
          const type = this.detectContentType(item.text)
          return type === contentTypeFilter.value
        })
      }

      // Türkçe: Tarih filtresi
      const dateFilter = context.app.activeFilters.find(f => f.id === 'date_range')
      if (dateFilter && dateFilter.value !== 'Tümü') {
        const now = Date.now()
        const ranges = {
          'Bugün': 24 * 60 * 60 * 1000,
          'Bu Hafta': 7 * 24 * 60 * 60 * 1000,
          'Bu Ay': 30 * 24 * 60 * 60 * 1000
        }
        
        const range = ranges[dateFilter.value]
        if (range) {
          filtered = filtered.filter(item => (now - item.timestamp) <= range)
        }
      }

      this.filteredHistory = filtered
    },

    detectContentType(text) {
      // Türkçe: URL kontrolü
      if (/^https?:\/\//.test(text)) return 'URL'
      
      // Türkçe: Email kontrolü
      if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(text)) return 'Email'
      
      // Türkçe: Kod kontrolü (basit)
      if (/^[\s]*[{}\[\]();]/.test(text) || text.includes('function') || text.includes('const ') || text.includes('let ')) {
        return 'Kod'
      }
      
      return 'Metin'
    },

    renderClipboardList() {
      const listContainer = document.getElementById('clipboard-list')

      if (this.filteredHistory.length === 0) {
        listContainer.innerHTML = `
          <div class="no-items">
            <i class="fas fa-search"></i>
            <p>Filtre kriterlerine uygun öğe bulunamadı</p>
          </div>
        `
        return
      }

      const itemsHTML = this.filteredHistory.map((item, index) => {
        const contentType = this.detectContentType(item.text)
        const preview = this.getTextPreview(item.text)
        const timeAgo = this.getTimeAgo(item.timestamp)

        return `
          <div class="clipboard-item" data-index="${index}" data-timestamp="${item.timestamp}">
            <div class="item-icon">
              ${this.getContentTypeIcon(contentType)}
            </div>
            <div class="item-content">
              <div class="item-preview">${preview}</div>
              <div class="item-meta">
                <span class="item-type">${contentType}</span>
                <span class="item-time">${timeAgo}</span>
                <span class="item-length">${item.text.length} karakter</span>
              </div>
            </div>
            <div class="item-actions">
              <button class="action-btn copy-btn" title="Kopyala">
                <i class="fas fa-copy"></i>
              </button>
              <button class="action-btn delete-btn" title="Sil">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        `
      }).join('')

      listContainer.innerHTML = itemsHTML

      // Türkçe: Olay dinleyicilerini ekle
      this.attachItemEventListeners()
    },

    attachItemEventListeners() {
      const items = document.querySelectorAll('.clipboard-item')
      
      items.forEach((item, index) => {
        // Türkçe: Öğe seçimi
        item.addEventListener('click', (e) => {
          if (!e.target.closest('.item-actions')) {
            this.selectItem(index)
          }
        })

        // Türkçe: Çift tıklama ile kopyala
        item.addEventListener('dblclick', () => {
          this.copyItem(index)
        })

        // Türkçe: Kopyala butonu
        const copyBtn = item.querySelector('.copy-btn')
        copyBtn.addEventListener('click', (e) => {
          e.stopPropagation()
          this.copyItem(index)
        })

        // Türkçe: Sil butonu
        const deleteBtn = item.querySelector('.delete-btn')
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation()
          this.deleteItem(index)
        })
      })
    },

    getContentTypeIcon(type) {
      const icons = {
        'Metin': '<i class="fas fa-font"></i>',
        'URL': '<i class="fas fa-link"></i>',
        'Email': '<i class="fas fa-envelope"></i>',
        'Kod': '<i class="fas fa-code"></i>'
      }
      return icons[type] || '<i class="fas fa-file-alt"></i>'
    },

    getTextPreview(text, maxLength = 60) {
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    },

    getTimeAgo(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 1) return 'Az önce'
      if (minutes < 60) return `${minutes} dakika önce`
      if (hours < 24) return `${hours} saat önce`
      return `${days} gün önce`
    },

    selectItem(index) {
      // Türkçe: Önceki seçimi temizle
      const prevSelected = document.querySelector('.clipboard-item.selected')
      if (prevSelected) {
        prevSelected.classList.remove('selected')
      }

      // Türkçe: Yeni seçimi işaretle
      const items = document.querySelectorAll('.clipboard-item')
      if (items[index]) {
        items[index].classList.add('selected')
        this.selectedItem = this.filteredHistory[index]
        this.showItemPreview(this.selectedItem)
        context.updateSelection(`${this.selectedItem.text.length} karakter seçildi`)
      }
    },

    showItemPreview(item) {
      const previewContainer = context.content.querySelector('.clipboard-preview')
      const contentType = this.detectContentType(item.text)

      previewContainer.innerHTML = `
        <div class="preview-header">
          <div class="preview-icon">
            ${this.getContentTypeIcon(contentType)}
          </div>
          <div class="preview-info">
            <h3>${contentType}</h3>
            <div class="preview-meta">
              <span><i class="fas fa-clock"></i> ${new Date(item.timestamp).toLocaleString('tr-TR')}</span>
              <span><i class="fas fa-text-width"></i> ${item.text.length} karakter</span>
              <span><i class="fas fa-paragraph"></i> ${item.text.split('\n').length} satır</span>
            </div>
          </div>
        </div>
        <div class="preview-content">
          <div class="content-display">
            ${this.formatContentForDisplay(item.text, contentType)}
          </div>
        </div>
        <div class="preview-actions">
          <button class="preview-action-btn primary" onclick="window.currentModuleInstance.copyItem()">
            <i class="fas fa-copy"></i> Kopyala
          </button>
          <button class="preview-action-btn" onclick="window.currentModuleInstance.deleteCurrentItem()">
            <i class="fas fa-trash"></i> Sil
          </button>
        </div>
      `
    },

    formatContentForDisplay(text, type) {
      switch (type) {
        case 'URL':
          return `<a href="${text}" target="_blank" class="url-link">${text}</a>`
        case 'Email':
          return `<a href="mailto:${text}" class="email-link">${text}</a>`
        case 'Kod':
          return `<pre class="code-block"><code>${this.escapeHtml(text)}</code></pre>`
        default:
          return `<div class="text-content">${this.escapeHtml(text).replace(/\n/g, '<br>')}</div>`
      }
    },

    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },

    updateStats() {
      const totalCount = document.getElementById('total-count')
      const lastUpdate = document.getElementById('last-update')

      totalCount.textContent = this.filteredHistory.length

      if (this.clipboardHistory.length > 0) {
        const latest = this.clipboardHistory[0]
        lastUpdate.textContent = this.getTimeAgo(latest.timestamp)
      } else {
        lastUpdate.textContent = '-'
      }
    },

    async copyItem(index = null) {
      const item = index !== null ? this.filteredHistory[index] : this.selectedItem
      if (!item) {
        context.updateStatus('Kopyalanacak öğe seçilmedi')
        return
      }

      try {
        await context.electronAPI.copyToClipboard(item.text)
        context.updateStatus('Panoya kopyalandı')
      } catch (error) {
        context.updateStatus('Kopyalama hatası')
      }
    },

    deleteItem(index) {
      if (confirm('Bu öğeyi silmek istediğinizden emin misiniz?')) {
        const item = this.filteredHistory[index]
        
        // Türkçe: Ana listeden kaldır
        const originalIndex = this.clipboardHistory.findIndex(h => h.timestamp === item.timestamp)
        if (originalIndex !== -1) {
          this.clipboardHistory.splice(originalIndex, 1)
        }

        this.applyFilters()
        this.renderClipboardList()
        this.updateStats()
        
        // Türkçe: Seçili öğe silinmişse önizlemeyi temizle
        if (this.selectedItem && this.selectedItem.timestamp === item.timestamp) {
          this.selectedItem = null
          context.content.innerHTML = `
            <div class="clipboard-preview">
              <div class="preview-placeholder">
                <i class="fas fa-clipboard-list"></i>
                <h3>Pano Öğesi Önizleme</h3>
                <p>Bir pano öğesi seçin ve burada detaylarını görün</p>
              </div>
            </div>
          `
        }

        context.updateStatus('Öğe silindi')
      }
    },

    deleteCurrentItem() {
      if (this.selectedItem) {
        const index = this.filteredHistory.findIndex(item => item.timestamp === this.selectedItem.timestamp)
        if (index !== -1) {
          this.deleteItem(index)
        }
      }
    },

    clearAllHistory() {
      if (confirm('Tüm pano geçmişini silmek istediğinizden emin misiniz?')) {
        this.clipboardHistory = []
        this.filteredHistory = []
        this.selectedItem = null
        this.renderClipboardList()
        this.updateStats()
        
        context.content.innerHTML = `
          <div class="clipboard-preview">
            <div class="preview-placeholder">
              <i class="fas fa-clipboard-list"></i>
              <h3>Pano Öğesi Önizleme</h3>
              <p>Bir pano öğesi seçin ve burada detaylarını görün</p>
            </div>
          </div>
        `
        
        context.updateStatus('Tüm geçmiş temizlendi')
      }
    },

    exportHistory() {
      if (this.clipboardHistory.length === 0) {
        context.updateStatus('Dışa aktarılacak veri yok')
        return
      }

      const exportData = this.clipboardHistory.map(item => ({
        text: item.text,
        type: this.detectContentType(item.text),
        timestamp: new Date(item.timestamp).toISOString(),
        length: item.text.length
      }))

      const jsonData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `pano-gecmisi-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      
      URL.revokeObjectURL(url)
      context.updateStatus('Geçmiş dışa aktarıldı')
    },

    executeAction(actionId) {
      switch (actionId) {
        case 'copy_item':
          this.copyItem()
          break
        case 'delete_item':
          this.deleteCurrentItem()
          break
        case 'clear_all':
          this.clearAllHistory()
          break
        case 'export_history':
          this.exportHistory()
          break
      }
    },

    onFiltersChanged(filters) {
      this.applyFilters()
      this.renderClipboardList()
      this.updateStats()
    }
  }

  // Türkçe: Global erişim için modül instance'ını kaydet
  window.currentModuleInstance = clipboardModule

  // Türkçe: Modülü başlat
  clipboardModule.init()
})(window.context)
