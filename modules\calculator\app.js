// Türkçe: Hesap makinesi modülü - gelişmiş matematiksel işlemler

;((context) => {
  const calculatorModule = {
    display: '0',
    previousValue: null,
    operation: null,
    waitingForOperand: false,
    history: JSON.parse(localStorage.getItem('calculator_history') || '[]'),
    memory: 0,

    init() {
      this.renderCalculator()
      this.setupEventListeners()
      this.setupKeyboardListeners()
      context.updateStatus("Hesap makinesi hazır")
    },

    renderCalculator() {
      context.content.innerHTML = `
        <div class="calculator-container">
          <div class="calculator">
            <div class="calculator-header">
              <h2><i class="fas fa-calculator"></i> Hesap Ma<PERSON></h2>
              <div class="calculator-controls">
                <button class="control-btn" id="history-btn" title="Geçmiş">
                  <i class="fas fa-history"></i>
                </button>
                <button class="control-btn" id="mode-btn" title="Mod Değiştir">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="calculator-display">
              <div class="display-expression" id="display-expression"></div>
              <div class="display-result" id="display-result">0</div>
            </div>
            
            <div class="calculator-memory" id="calculator-memory">
              <button class="memory-btn" data-action="mc">MC</button>
              <button class="memory-btn" data-action="mr">MR</button>
              <button class="memory-btn" data-action="m+">M+</button>
              <button class="memory-btn" data-action="m-">M-</button>
              <button class="memory-btn" data-action="ms">MS</button>
            </div>
            
            <div class="calculator-buttons" id="calculator-buttons">
              ${this.renderButtons()}
            </div>
          </div>
          
          <div class="calculator-sidebar" id="calculator-sidebar">
            <div class="history-panel" id="history-panel">
              <h3><i class="fas fa-history"></i> Geçmiş</h3>
              <div class="history-list" id="history-list">
                ${this.renderHistory()}
              </div>
              <button class="clear-history-btn" id="clear-history-btn">
                <i class="fas fa-trash"></i> Geçmişi Temizle
              </button>
            </div>
          </div>
        </div>
      `
    },

    renderButtons() {
      const mode = this.getCurrentMode()
      
      if (mode === 'Bilimsel') {
        return this.renderScientificButtons()
      } else if (mode === 'Programcı') {
        return this.renderProgrammerButtons()
      } else {
        return this.renderStandardButtons()
      }
    },

    renderStandardButtons() {
      return `
        <div class="button-row">
          <button class="calc-btn function" data-action="clear">C</button>
          <button class="calc-btn function" data-action="clear-entry">CE</button>
          <button class="calc-btn function" data-action="backspace">⌫</button>
          <button class="calc-btn operator" data-action="divide">÷</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="7">7</button>
          <button class="calc-btn number" data-value="8">8</button>
          <button class="calc-btn number" data-value="9">9</button>
          <button class="calc-btn operator" data-action="multiply">×</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="4">4</button>
          <button class="calc-btn number" data-value="5">5</button>
          <button class="calc-btn number" data-value="6">6</button>
          <button class="calc-btn operator" data-action="subtract">−</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="1">1</button>
          <button class="calc-btn number" data-value="2">2</button>
          <button class="calc-btn number" data-value="3">3</button>
          <button class="calc-btn operator" data-action="add">+</button>
        </div>
        <div class="button-row">
          <button class="calc-btn function" data-action="negate">±</button>
          <button class="calc-btn number" data-value="0">0</button>
          <button class="calc-btn number" data-value=".">.</button>
          <button class="calc-btn equals" data-action="equals">=</button>
        </div>
      `
    },

    renderScientificButtons() {
      return `
        <div class="button-row">
          <button class="calc-btn function" data-action="clear">C</button>
          <button class="calc-btn function" data-action="clear-entry">CE</button>
          <button class="calc-btn function" data-action="backspace">⌫</button>
          <button class="calc-btn operator" data-action="divide">÷</button>
          <button class="calc-btn function" data-action="sqrt">√</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="7">7</button>
          <button class="calc-btn number" data-value="8">8</button>
          <button class="calc-btn number" data-value="9">9</button>
          <button class="calc-btn operator" data-action="multiply">×</button>
          <button class="calc-btn function" data-action="square">x²</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="4">4</button>
          <button class="calc-btn number" data-value="5">5</button>
          <button class="calc-btn number" data-value="6">6</button>
          <button class="calc-btn operator" data-action="subtract">−</button>
          <button class="calc-btn function" data-action="power">xʸ</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="1">1</button>
          <button class="calc-btn number" data-value="2">2</button>
          <button class="calc-btn number" data-value="3">3</button>
          <button class="calc-btn operator" data-action="add">+</button>
          <button class="calc-btn function" data-action="log">log</button>
        </div>
        <div class="button-row">
          <button class="calc-btn function" data-action="negate">±</button>
          <button class="calc-btn number" data-value="0">0</button>
          <button class="calc-btn number" data-value=".">.</button>
          <button class="calc-btn equals" data-action="equals">=</button>
          <button class="calc-btn function" data-action="ln">ln</button>
        </div>
        <div class="button-row">
          <button class="calc-btn function" data-action="sin">sin</button>
          <button class="calc-btn function" data-action="cos">cos</button>
          <button class="calc-btn function" data-action="tan">tan</button>
          <button class="calc-btn function" data-action="pi">π</button>
          <button class="calc-btn function" data-action="e">e</button>
        </div>
      `
    },

    renderProgrammerButtons() {
      return `
        <div class="button-row">
          <button class="calc-btn function" data-action="clear">C</button>
          <button class="calc-btn function" data-action="clear-entry">CE</button>
          <button class="calc-btn function" data-action="backspace">⌫</button>
          <button class="calc-btn operator" data-action="divide">÷</button>
        </div>
        <div class="button-row">
          <button class="calc-btn hex" data-value="A">A</button>
          <button class="calc-btn hex" data-value="B">B</button>
          <button class="calc-btn hex" data-value="C">C</button>
          <button class="calc-btn hex" data-value="D">D</button>
        </div>
        <div class="button-row">
          <button class="calc-btn hex" data-value="E">E</button>
          <button class="calc-btn hex" data-value="F">F</button>
          <button class="calc-btn function" data-action="and">AND</button>
          <button class="calc-btn function" data-action="or">OR</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="7">7</button>
          <button class="calc-btn number" data-value="8">8</button>
          <button class="calc-btn number" data-value="9">9</button>
          <button class="calc-btn operator" data-action="multiply">×</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="4">4</button>
          <button class="calc-btn number" data-value="5">5</button>
          <button class="calc-btn number" data-value="6">6</button>
          <button class="calc-btn operator" data-action="subtract">−</button>
        </div>
        <div class="button-row">
          <button class="calc-btn number" data-value="1">1</button>
          <button class="calc-btn number" data-value="2">2</button>
          <button class="calc-btn number" data-value="3">3</button>
          <button class="calc-btn operator" data-action="add">+</button>
        </div>
        <div class="button-row">
          <button class="calc-btn function" data-action="negate">±</button>
          <button class="calc-btn number" data-value="0">0</button>
          <button class="calc-btn number" data-value=".">.</button>
          <button class="calc-btn equals" data-action="equals">=</button>
        </div>
      `
    },

    renderHistory() {
      if (this.history.length === 0) {
        return '<div class="no-history">Henüz işlem geçmişi yok</div>'
      }

      return this.history.slice(-10).reverse().map(item => `
        <div class="history-item" data-expression="${item.expression}" data-result="${item.result}">
          <div class="history-expression">${item.expression}</div>
          <div class="history-result">${item.result}</div>
          <div class="history-time">${new Date(item.timestamp).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}</div>
        </div>
      `).join('')
    },

    setupEventListeners() {
      // Türkçe: Hesap makinesi butonları
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('calc-btn')) {
          this.handleButtonClick(e.target)
        }
      })

      // Türkçe: Bellek butonları
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('memory-btn')) {
          this.handleMemoryAction(e.target.dataset.action)
        }
      })

      // Türkçe: Geçmiş öğeleri
      document.addEventListener('click', (e) => {
        if (e.target.closest('.history-item')) {
          const item = e.target.closest('.history-item')
          this.loadFromHistory(item.dataset.expression, item.dataset.result)
        }
      })

      // Türkçe: Kontrol butonları
      const historyBtn = document.getElementById('history-btn')
      const modeBtn = document.getElementById('mode-btn')
      const clearHistoryBtn = document.getElementById('clear-history-btn')

      historyBtn.addEventListener('click', () => {
        this.toggleHistoryPanel()
      })

      modeBtn.addEventListener('click', () => {
        this.cycleModes()
      })

      clearHistoryBtn.addEventListener('click', () => {
        this.clearHistory()
      })
    },

    setupKeyboardListeners() {
      document.addEventListener('keydown', (e) => {
        // Türkçe: Sadece hesap makinesi modülü aktifken klavye olaylarını dinle
        if (context.app.currentModule?.id !== 'calculator') return

        e.preventDefault()

        const key = e.key
        
        if (/[0-9]/.test(key)) {
          this.inputNumber(key)
        } else if (key === '.') {
          this.inputNumber('.')
        } else if (key === '+') {
          this.performOperation('add')
        } else if (key === '-') {
          this.performOperation('subtract')
        } else if (key === '*') {
          this.performOperation('multiply')
        } else if (key === '/') {
          this.performOperation('divide')
        } else if (key === 'Enter' || key === '=') {
          this.calculate()
        } else if (key === 'Escape') {
          this.clear()
        } else if (key === 'Backspace') {
          this.backspace()
        }
      })
    },

    handleButtonClick(button) {
      if (button.classList.contains('number')) {
        this.inputNumber(button.dataset.value)
      } else if (button.classList.contains('operator')) {
        this.performOperation(button.dataset.action)
      } else if (button.classList.contains('equals')) {
        this.calculate()
      } else if (button.classList.contains('function')) {
        this.performFunction(button.dataset.action)
      } else if (button.classList.contains('hex')) {
        this.inputNumber(button.dataset.value)
      }
    },

    inputNumber(value) {
      if (this.waitingForOperand) {
        this.display = value
        this.waitingForOperand = false
      } else {
        this.display = this.display === '0' ? value : this.display + value
      }
      
      this.updateDisplay()
    },

    performOperation(nextOperation) {
      const inputValue = parseFloat(this.display)

      if (this.previousValue === null) {
        this.previousValue = inputValue
      } else if (this.operation) {
        const currentValue = this.previousValue || 0
        const newValue = this.performCalculation(this.operation, currentValue, inputValue)

        this.display = String(newValue)
        this.previousValue = newValue
      }

      this.waitingForOperand = true
      this.operation = nextOperation
      this.updateDisplay()
    },

    performCalculation(operation, firstValue, secondValue) {
      switch (operation) {
        case 'add':
          return firstValue + secondValue
        case 'subtract':
          return firstValue - secondValue
        case 'multiply':
          return firstValue * secondValue
        case 'divide':
          return secondValue !== 0 ? firstValue / secondValue : 0
        case 'power':
          return Math.pow(firstValue, secondValue)
        case 'and':
          return parseInt(firstValue) & parseInt(secondValue)
        case 'or':
          return parseInt(firstValue) | parseInt(secondValue)
        default:
          return secondValue
      }
    },

    performFunction(func) {
      const value = parseFloat(this.display)
      let result

      switch (func) {
        case 'clear':
          this.clear()
          return
        case 'clear-entry':
          this.display = '0'
          break
        case 'backspace':
          this.backspace()
          return
        case 'negate':
          result = -value
          break
        case 'sqrt':
          result = Math.sqrt(value)
          break
        case 'square':
          result = value * value
          break
        case 'log':
          result = Math.log10(value)
          break
        case 'ln':
          result = Math.log(value)
          break
        case 'sin':
          result = this.isRadianMode() ? Math.sin(value) : Math.sin(value * Math.PI / 180)
          break
        case 'cos':
          result = this.isRadianMode() ? Math.cos(value) : Math.cos(value * Math.PI / 180)
          break
        case 'tan':
          result = this.isRadianMode() ? Math.tan(value) : Math.tan(value * Math.PI / 180)
          break
        case 'pi':
          result = Math.PI
          break
        case 'e':
          result = Math.E
          break
        default:
          return
      }

      this.display = String(result)
      this.updateDisplay()
    },

    calculate() {
      const inputValue = parseFloat(this.display)

      if (this.previousValue !== null && this.operation) {
        const newValue = this.performCalculation(this.operation, this.previousValue, inputValue)
        
        // Türkçe: Geçmişe ekle
        this.addToHistory(`${this.previousValue} ${this.getOperationSymbol(this.operation)} ${inputValue}`, newValue)
        
        this.display = String(newValue)
        this.previousValue = null
        this.operation = null
        this.waitingForOperand = true
      }

      this.updateDisplay()
    },

    getOperationSymbol(operation) {
      const symbols = {
        'add': '+',
        'subtract': '−',
        'multiply': '×',
        'divide': '÷',
        'power': '^',
        'and': '&',
        'or': '|'
      }
      return symbols[operation] || operation
    },

    clear() {
      this.display = '0'
      this.previousValue = null
      this.operation = null
      this.waitingForOperand = false
      this.updateDisplay()
    },

    backspace() {
      if (this.display.length > 1) {
        this.display = this.display.slice(0, -1)
      } else {
        this.display = '0'
      }
      this.updateDisplay()
    },

    handleMemoryAction(action) {
      const value = parseFloat(this.display)

      switch (action) {
        case 'mc':
          this.memory = 0
          break
        case 'mr':
          this.display = String(this.memory)
          break
        case 'm+':
          this.memory += value
          break
        case 'm-':
          this.memory -= value
          break
        case 'ms':
          this.memory = value
          break
      }

      this.updateDisplay()
      this.updateMemoryIndicator()
    },

    updateDisplay() {
      const displayResult = document.getElementById('display-result')
      const displayExpression = document.getElementById('display-expression')

      displayResult.textContent = this.formatNumber(this.display)

      // Türkçe: İfade gösterimi
      let expression = ''
      if (this.previousValue !== null && this.operation) {
        expression = `${this.formatNumber(this.previousValue)} ${this.getOperationSymbol(this.operation)}`
      }
      displayExpression.textContent = expression
    },

    formatNumber(num) {
      const number = parseFloat(num)
      if (isNaN(number)) return '0'
      
      // Türkçe: Çok büyük veya çok küçük sayılar için bilimsel notasyon
      if (Math.abs(number) >= 1e15 || (Math.abs(number) < 1e-6 && number !== 0)) {
        return number.toExponential(6)
      }
      
      // Türkçe: Ondalık basamak sınırlaması
      return number.toString().length > 12 ? number.toPrecision(12) : number.toString()
    },

    updateMemoryIndicator() {
      const memoryButtons = document.querySelectorAll('.memory-btn')
      memoryButtons.forEach(btn => {
        btn.classList.toggle('has-memory', this.memory !== 0)
      })
    },

    addToHistory(expression, result) {
      this.history.push({
        expression,
        result: this.formatNumber(result),
        timestamp: Date.now()
      })

      // Türkçe: Maksimum 50 işlem tut
      if (this.history.length > 50) {
        this.history = this.history.slice(-50)
      }

      localStorage.setItem('calculator_history', JSON.stringify(this.history))
      this.updateHistoryDisplay()
    },

    updateHistoryDisplay() {
      const historyList = document.getElementById('history-list')
      if (historyList) {
        historyList.innerHTML = this.renderHistory()
      }
    },

    loadFromHistory(expression, result) {
      this.display = result
      this.previousValue = null
      this.operation = null
      this.waitingForOperand = false
      this.updateDisplay()
      context.updateStatus('Geçmişten yüklendi')
    },

    clearHistory() {
      if (confirm('Hesaplama geçmişini temizlemek istediğinizden emin misiniz?')) {
        this.history = []
        localStorage.removeItem('calculator_history')
        this.updateHistoryDisplay()
        context.updateStatus('Geçmiş temizlendi')
      }
    },

    toggleHistoryPanel() {
      const sidebar = document.getElementById('calculator-sidebar')
      sidebar.classList.toggle('show')
    },

    getCurrentMode() {
      const modeFilter = context.app.activeFilters.find(f => f.id === 'calc_mode')
      return modeFilter ? modeFilter.value : 'Standart'
    },

    isRadianMode() {
      const angleFilter = context.app.activeFilters.find(f => f.id === 'angle_unit')
      return angleFilter ? angleFilter.value === 'Radyan' : false
    },

    cycleModes() {
      const modes = ['Standart', 'Bilimsel', 'Programcı']
      const currentMode = this.getCurrentMode()
      const currentIndex = modes.indexOf(currentMode)
      const nextIndex = (currentIndex + 1) % modes.length
      const nextMode = modes[nextIndex]

      // Türkçe: Filtreyi güncelle
      context.addFilter('calc_mode', 'Mod', 'dropdown', [nextMode])
      
      // Türkçe: Butonları yeniden render et
      const buttonsContainer = document.getElementById('calculator-buttons')
      buttonsContainer.innerHTML = this.renderButtons()
      
      context.updateStatus(`${nextMode} moda geçildi`)
    },

    executeAction(actionId) {
      switch (actionId) {
        case 'clear_all':
          this.clear()
          break
        case 'copy_result':
          this.copyResult()
          break
        case 'show_history':
          this.toggleHistoryPanel()
          break
      }
    },

    copyResult() {
      context.electronAPI.copyToClipboard(this.display)
      context.updateStatus('Sonuç kopyalandı')
    },

    onFiltersChanged(filters) {
      // Türkçe: Mod değiştiğinde butonları yeniden render et
      const modeFilter = filters.find(f => f.id === 'calc_mode')
      if (modeFilter) {
        const buttonsContainer = document.getElementById('calculator-buttons')
        if (buttonsContainer) {
          buttonsContainer.innerHTML = this.renderButtons()
        }
      }
    }
  }

  // Türkçe: Global erişim için modül instance'ını kaydet
  window.currentModuleInstance = calculatorModule

  // Türkçe: Modülü başlat
  calculatorModule.init()
})(window.context)
