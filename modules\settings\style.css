/* Türkçe: <PERSON><PERSON><PERSON> modülü stilleri */

.settings-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.settings-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settings-actions {
  display: flex;
  gap: 1rem;
}

.settings-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.settings-btn.primary {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.settings-btn.primary:hover {
  background: var(--accent-hover);
}

.settings-btn.secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
}

.settings-btn.secondary:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.settings-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.settings-tabs {
  width: 250px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 1rem 0;
  overflow-y: auto;
}

.tab-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.tab-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.tab-btn.active {
  background: var(--accent-color);
  color: white;
  border-right: 3px solid var(--accent-hover);
}

.tab-btn i {
  width: 20px;
  text-align: center;
}

.settings-panels {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.settings-panel {
  display: none;
}

.settings-panel.active {
  display: block;
}

.settings-panel h3 {
  margin: 0 0 2rem 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.setting-group {
  margin-bottom: 2rem;
}

.setting-label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.setting-label > span {
  font-size: 0.875rem;
}

.setting-input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.setting-input:focus {
  outline: none;
  border-color: var(--accent-color);
}

.setting-checkbox {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  color: var(--text-primary);
  font-weight: 500;
}

.setting-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-secondary);
  position: relative;
  transition: all 0.2s ease;
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.setting-group small {
  color: var(--text-muted);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

.setting-group small i {
  margin-right: 0.25rem;
}

.setting-group small a {
  color: var(--accent-color);
  text-decoration: none;
}

.setting-group small a:hover {
  text-decoration: underline;
}

/* Türkçe: Tema seçici */
.theme-selector {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.theme-option:hover {
  border-color: var(--accent-color);
}

.theme-option input[type="radio"] {
  display: none;
}

.theme-option input[type="radio"]:checked + .theme-preview {
  box-shadow: 0 0 0 2px var(--accent-color);
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.theme-preview.light {
  background: #ffffff;
}

.theme-preview.light .theme-header {
  height: 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.theme-preview.light .theme-content {
  height: 27px;
  background: #ffffff;
}

.theme-preview.dark {
  background: #1a1a1a;
}

.theme-preview.dark .theme-header {
  height: 12px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.theme-preview.dark .theme-content {
  height: 27px;
  background: #1a1a1a;
}

.theme-option span {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Türkçe: Modül listesi */
.modules-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.module-setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.module-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.module-info i {
  font-size: 1.5rem;
  color: var(--accent-color);
  width: 24px;
  text-align: center;
}

.module-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.module-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.setting-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.setting-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.setting-toggle input:checked + .toggle-slider {
  background-color: var(--accent-color);
}

.setting-toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.setting-toggle input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Türkçe: API anahtarı */
.api-key-input {
  position: relative;
  display: flex;
}

.api-key-input input {
  flex: 1;
  padding-right: 3rem;
}

.toggle-visibility-btn {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
}

.toggle-visibility-btn:hover {
  color: var(--text-primary);
}

.api-status {
  margin-top: 1.5rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.api-status h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.status-indicator.connected {
  color: var(--success-color);
}

.status-indicator.error {
  color: var(--danger-color);
}

.status-indicator.checking {
  color: var(--warning-color);
}

.api-usage {
  margin-top: 1.5rem;
}

.api-usage h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-card {
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Türkçe: Veri yönetimi */
.data-actions,
.backup-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.data-btn,
.backup-btn {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.data-btn:hover,
.backup-btn:hover {
  background: var(--bg-tertiary);
}

.data-btn.danger {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.data-btn.danger:hover {
  background: var(--danger-color);
  color: white;
}

.app-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.info-value {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
}

/* Türkçe: Responsive tasarım */
@media (max-width: 768px) {
  .settings-content {
    flex-direction: column;
  }

  .settings-tabs {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .tab-btn {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }

  .settings-panels {
    padding: 1rem;
  }

  .theme-selector {
    flex-direction: column;
  }

  .data-actions,
  .backup-actions {
    flex-direction: column;
  }

  .usage-stats {
    grid-template-columns: 1fr;
  }
}

/* Türkçe: Scrollbar stilleri */
.settings-tabs::-webkit-scrollbar,
.settings-panels::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.settings-tabs::-webkit-scrollbar-track,
.settings-panels::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.settings-tabs::-webkit-scrollbar-thumb,
.settings-panels::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.settings-tabs::-webkit-scrollbar-thumb:hover,
.settings-panels::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
