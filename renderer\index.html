<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modular Desktop App</title>
    
    <!-- Türkçe: FontAwesome ikonları -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Türkçe: Ana stil dosyası -->
    <link rel="stylesheet" href="style.css">
</head>
<body class="theme-dark">
    <!-- Türkçe: <PERSON><PERSON> başlık çubuğu -->
    <div class="titlebar">
        <div class="titlebar-drag-region">
            <div class="titlebar-left">
                <div class="traffic-lights">
                    <button class="traffic-light close" id="close-btn"></button>
                    <button class="traffic-light minimize" id="minimize-btn"></button>
                    <button class="traffic-light maximize" id="maximize-btn"></button>
                </div>
                <span class="app-title">Modular Desktop App</span>
            </div>
        </div>
    </div>

    <!-- Türkçe: Ana uygulama alanı -->
    <div class="app-container">
        <!-- Türkçe: Header - Modül seçici, filtreler ve arama -->
        <div class="app-header">
            <!-- Türkçe: Sol - Modül seçici -->
            <div class="module-selector">
                <button class="current-module-btn" id="current-module-btn">
                    <i class="fas fa-home" id="current-module-icon"></i>
                    <span id="current-module-name">Home</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="module-dropdown" id="module-dropdown">
                    <!-- Modül listesi buraya yüklenecek -->
                </div>
            </div>

            <!-- Türkçe: Orta Sol - Filtreler -->
            <div class="filters-container" id="filters-container">
                <!-- Aktif modülün filtreleri buraya yüklenecek -->
            </div>

            <!-- Türkçe: Sağ - Ana arama input -->
            <div class="main-search">
                <div class="search-container">
                    <input type="text" class="main-input" id="main-input" 
                           placeholder="Search anything or use commands (!fs, !web, !ai...)">
                </div>
            </div>
        </div>

        <!-- Türkçe: Ana içerik alanı -->
        <div class="main-content" id="main-content">
            <!-- Modül içerikleri buraya yüklenecek -->
        </div>

        <!-- Türkçe: Durum çubuğu -->
        <div class="status-bar">
            <div class="status-left">
                <span class="status-text" id="status-text">Ready</span>
            </div>
            <div class="status-right">
                <span class="selection-info" id="selection-info"></span>
            </div>
        </div>
    </div>

    <!-- Türkçe: API Key Modal -->
    <div class="modal-overlay" id="api-modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3>API Settings</h3>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="gemini-api-key">Gemini AI API Key:</label>
                    <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key...">
                    <small>Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modal-cancel">Cancel</button>
                <button class="btn btn-primary" id="modal-save">Save</button>
            </div>
        </div>
    </div>

    <!-- Türkçe: Ana JavaScript dosyası -->
    <script src="app.js"></script>
</body>
</html>
