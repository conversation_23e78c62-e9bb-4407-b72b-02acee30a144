// Türkçe: Preload scripti - renderer ve main process arasında gü<PERSON>li kö<PERSON>rü
const { contextBridge, ipcRenderer } = require("electron")

// Türkçe: Güvenli API'leri renderer process'e expose et
contextBridge.exposeInMainWorld("electronAPI", {
  // Türkçe: Pencere kontrolü
  minimizeWindow: () => ipcRenderer.invoke("window-minimize"),
  maximizeWindow: () => ipcRenderer.invoke("window-maximize"),
  closeWindow: () => ipcRenderer.invoke("window-close"),

  // Türkçe: Modül sistemi
  scanModules: () => ipcRenderer.invoke("scan-modules"),
  loadModuleFiles: (modulePath) => ipcRenderer.invoke("load-module-files", modulePath),

  // Türkçe: Dosya sistemi
  searchFiles: (query, filters) => ipcRenderer.invoke("search-files", query, filters),
  openFile: (filePath) => ipcRenderer.invoke("open-file", filePath),
  showInFolder: (filePath) => ipcRenderer.invoke("show-in-folder", filePath),

  // Türkçe: Sistem komutları
  runCommand: (command) => ipcRenderer.invoke("run-command", command),

  // Türkçe: Pano işlemleri
  getClipboardHistory: () => ipcRenderer.invoke("get-clipboard-history"),
  copyToClipboard: (text) => ipcRenderer.invoke("copy-to-clipboard", text),
})
